# Database Configuration
DB_HOST=localhost
DB_PORT=3306
DB_USER=root
DB_PASSWORD=
DB_NAME=myapp
DB_CONNECTION_LIMIT=20
DB_IDLE_TIMEOUT=60000

# Database Startup Retry Configuration
DB_STARTUP_RETRIES=5
DB_STARTUP_RETRY_DELAY=2000

# Database Connection Behavior
REQUIRE_DB_ON_STARTUP=false
DB_RECONNECT_INTERVAL_MS=30000

# Restart Manager Configuration
MAX_RESTARTS=5
RESTART_WINDOW_MS=300000
RESTART_INITIAL_DELAY_MS=1000
RESTART_MAX_DELAY_MS=60000
RESTART_BACKOFF_MULTIPLIER=2
AUTO_RESTART_ENABLED=true

# Application Configuration
APP_PORT=3000
LOG_LEVEL=info
RATE_LIMIT_ENABLED=true

# Health Monitor Configuration
HEALTH_CHECK_INTERVAL_MS=30000
HEALTH_CHECK_UNHEALTHY_THRESHOLD=3
HEALTH_CHECK_MEMORY_THRESHOLD_MB=512

# Competition Refresher Configuration
COMPETITION_REFRESH_INTERVAL_MS=10000

# WordPress connection behavior
WP_CONNECTION_ENABLED=true
REQUIRE_WP_ON_STARTUP=false

