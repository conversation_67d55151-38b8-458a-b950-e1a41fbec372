const express = require('express');
const helmet = require('helmet');
const cors = require('cors');
const rateLimit = require('express-rate-limit');
const { logger } = require('./shared/lib/logger');
const { errorHandler } = require('./shared/middleware/error-handler');
const { apiRouter } = require('./api');
const { dbConnect } = require('./shared/lib/db');
const { restartManager } = require('./shared/lib/restart-manager');

const app = express();

// Security middleware
app.use(helmet());
app.use(cors());
app.use(express.json());

// Rate limiting
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // limit each IP to 100 requests per windowMs
});
app.use(limiter);

// Routes
app.use('/api', apiRouter);

// Error handling
app.use(errorHandler);

// Start server
const APP_PORT = process.env.APP_PORT || 3000;

const startServer = async () => {
  try {
    // Test database connection with retry logic
    logger.info('Testing database connection...');
    const maxRetries = parseInt(process.env.DB_STARTUP_RETRIES || '5', 10);
    const retryDelay = parseInt(process.env.DB_STARTUP_RETRY_DELAY || '2000', 10);

    await dbConnect.testConnectionWithRetry(maxRetries, retryDelay);
    logger.info('Database connection successful');

    app.listen(APP_PORT, () => {
      logger.info(`App.js Server running on port ${APP_PORT}`);
    });
  } catch (error) {
    logger.error('Failed to start server:', error);

    // Use restart manager instead of immediate exit
    await restartManager.handleStartupFailure(`Database connection failed: ${error}`);
  }
};

startServer();

module.exports = { app };
