const express = require('express');
const http = require('http');
const { Server } = require('socket.io');

const app = express();
const server = http.createServer(app);
const io = new Server(server, {
  cors: {
    origin: '*',
    methods: ['GET', 'POST'],
  },
  transports: ['websocket', 'polling'],
  pingTimeout: 60000,
  pingInterval: 25000,
});
// path: '/custom/socket.io',

// Serve a simple HTML page for manual testing
app.get('/', (req, res) => {
  res.send(`
    <!DOCTYPE html>
    <html>
    <head>
      <title>Socket.IO Test</title>
      <script src="https://cdn.socket.io/4.7.4/socket.io.min.js"></script>
      <script>
        document.addEventListener('DOMContentLoaded', () => {
          const socket = io({
            path: '/custom/socket.io',
            transports: ['websocket', 'polling']
          });
          
          socket.on('connect', () => {
            console.log('Connected to server with ID:', socket.id);
            document.getElementById('status').textContent = 'Connected: ' + socket.id;
          });
          
          socket.on('disconnect', (reason) => {
            console.log('Disconnected:', reason);
            document.getElementById('status').textContent = 'Disconnected: ' + reason;
          });
          
          socket.on('response', (data) => {
            console.log('Response:', data);
            document.getElementById('messages').innerHTML += '<div>Response: ' + JSON.stringify(data) + '</div>';
          });
          
          document.getElementById('sendBtn').addEventListener('click', () => {
            socket.emit('message', { text: 'Hello from browser' });
            document.getElementById('messages').innerHTML += '<div>Sent: Hello from browser</div>';
          });
        });
      </script>
    </head>
    <body>
      <h1>Socket.IO Test Client</h1>
      <div id="status">Disconnected</div>
      <button id="sendBtn">Send Message</button>
      <div id="messages"></div>
    </body>
    </html>
  `);
});

io.on('connection', socket => {
  console.log('Client connected:', socket.id);

  // Send an immediate welcome message
  socket.emit('response', { message: 'Welcome!', id: socket.id });

  // Set up a keep-alive ping
  const pingInterval = setInterval(() => {
    if (socket.connected) {
      console.log('Sending ping to client:', socket.id);
      socket.emit('response', { type: 'ping', timestamp: new Date().toISOString() });
    } else {
      clearInterval(pingInterval);
    }
  }, 5000);

  socket.on('disconnect', reason => {
    console.log('Client disconnected:', socket.id, 'Reason:', reason);
    clearInterval(pingInterval);
  });

  socket.on('message', data => {
    console.log('Received message from', socket.id, ':', data);
    socket.emit('response', { status: 'ok', received: data });
  });
});

const PORT = 3001;
server.listen(PORT, () => {
  console.log(`Test Socket.IO server running on port ${PORT}`);
  console.log(`Open http://localhost:${PORT} in your browser to test manually`);
});
