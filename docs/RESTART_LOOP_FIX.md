# Restart Loop Fix - Health Monitor Graceful Degradation

## Problem

When `REQUIRE_DB_ON_STARTUP=false` was set (graceful degradation mode), the health monitor was still treating database connection failures as "unhealthy" and triggering application restarts. This caused a restart loop:

```
warn: Health check failed (3/3): database
warn: Attempting restart #1 due to: Health check failures: database: Database connection failed: Error: connect ETIMEDOUT
info: Restarting application (attempt 1/5)
[nodemon] clean exit - waiting for changes before restart
```

## Root Cause

The health monitor's `checkDatabase()` function was always returning `status: 'unhealthy'` for database connection failures, regardless of whether the application was configured for graceful degradation.

## Solution

Updated the health monitor to be aware of the graceful degradation setting:

### Before (Problematic)
```typescript
catch (error) {
  return {
    name: 'database',
    status: 'unhealthy',  // Always unhealthy
    message: `Database connection failed: ${error}`,
    timestamp: Date.now(),
    responseTime: Date.now() - startTime,
  };
}
```

### After (Fixed)
```typescript
catch (error) {
  // In graceful degradation mode, database failures are warnings, not unhealthy
  const requireDbOnStartup = process.env.REQUIRE_DB_ON_STARTUP !== 'false';
  const status = requireDbOnStartup ? 'unhealthy' : 'warning';
  const message = requireDbOnStartup 
    ? `Database connection failed: ${error}`
    : `Database unavailable (graceful degradation mode): ${error}`;
  
  return {
    name: 'database',
    status,
    message,
    timestamp: Date.now(),
    responseTime: Date.now() - startTime,
  };
}
```

## Behavior Now

### Graceful Degradation Mode (`REQUIRE_DB_ON_STARTUP=false`)
- ✅ **Server starts** even without database
- ✅ **Health monitor runs** but treats DB failures as warnings
- ✅ **No restart loops** - application stays running
- ✅ **Background reconnection** attempts continue
- ✅ **Automatic recovery** when database returns

### Strict Mode (`REQUIRE_DB_ON_STARTUP=true`)
- ❌ **Server won't start** without database
- ❌ **Health monitor treats** DB failures as unhealthy
- 🔄 **Restart manager** handles failures with backoff
- ✅ **Clear failure state** for monitoring

## Expected Log Output (Graceful Mode)

Instead of restart loops, you should now see:

```
info: Server running on port 3000
warn: Database connection failed during startup: connect ETIMEDOUT
warn: Server starting without database connection - some features may be unavailable
info: Database reconnection loop started (interval: 30000ms)
warn: Health check warning - database: Database unavailable (graceful degradation mode): Error: connect ETIMEDOUT
info: Attempting database reconnection...
```

## Configuration

Your current `.env` settings are correct for graceful degradation:

```bash
# Graceful degradation enabled
REQUIRE_DB_ON_STARTUP=false

# Health monitor settings
HEALTH_MONITOR_ENABLED=true
HEALTH_CHECK_INTERVAL_MS=30000
HEALTH_UNHEALTHY_THRESHOLD=3
HEALTH_MEMORY_THRESHOLD_MB=512

# Background reconnection
DB_RECONNECT_INTERVAL_MS=30000
```

## Testing

To verify the fix works:

1. **Start your server** with database unavailable
2. **Check logs** - should see graceful degradation messages, no restart loops
3. **Test health endpoint**: `curl http://localhost:3000/health`
4. **Verify server responds** to basic requests
5. **When database returns** - services should automatically restart

## Health Check Response

With database unavailable, health endpoint should return:

```json
{
  "status": "degraded",
  "database": {
    "status": "disconnected",
    "connectionState": "disconnected",
    "message": "Database is unavailable - running in degraded mode"
  },
  "uptime": 123.45
}
```

## Monitoring

The application will now:
- ✅ **Stay running** during database outages
- ✅ **Log warnings** instead of errors for DB unavailability
- ✅ **Automatically recover** when database returns
- ✅ **Provide health status** for monitoring systems
- ✅ **Maintain service availability** for non-database features

This provides much better resilience and eliminates the restart loop issue you were experiencing.
