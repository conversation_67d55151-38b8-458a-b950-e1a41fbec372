# Database Connection Handling

This document explains how the application handles database connection failures and implements automatic recovery mechanisms.

## Overview

The application now includes robust database connection handling with two operational modes:

### 1. Strict Mode (Traditional)
- Server requires database connection to start
- Fails fast if database unavailable
- Uses restart manager for recovery
- Best for: Applications where database is absolutely critical

### 2. Graceful Degradation Mode (Recommended)
- Server starts even without database
- Provides limited functionality when database unavailable
- Automatic background reconnection
- Best for: High-availability applications

## Configuration

Choose your mode with the `REQUIRE_DB_ON_STARTUP` environment variable:

```bash
# Graceful degradation (recommended for production)
REQUIRE_DB_ON_STARTUP=false

# Strict mode (traditional behavior)
REQUIRE_DB_ON_STARTUP=true
```

## Features

### Startup Connection Retry

When the application starts, it will attempt to connect to the database multiple times before giving up:

- **Default retries**: 5 attempts
- **Default delay**: 2 seconds (with exponential backoff)
- **Configurable**: Set via environment variables

### Restart Manager Integration

If database connection fails during startup after all retries:

- The restart manager will attempt to restart the application
- Uses exponential backoff between restart attempts
- Limits the number of restarts within a time window
- Logs detailed information about restart attempts

### Runtime Connection Handling

During runtime, database connection issues are handled by:

- Connection pooling with automatic reconnection
- Health monitoring that detects database issues
- Automatic restart when health checks fail consistently

## Configuration

### Environment Variables

```bash
# Database startup retry configuration
DB_STARTUP_RETRIES=5                    # Number of connection attempts during startup
DB_STARTUP_RETRY_DELAY=2000            # Initial delay between retries (ms)

# Restart manager configuration
MAX_RESTARTS=5                         # Maximum restarts within the time window
RESTART_WINDOW_MS=300000               # Time window for restart counting (5 minutes)
RESTART_INITIAL_DELAY_MS=1000          # Initial delay before first restart
RESTART_MAX_DELAY_MS=60000             # Maximum delay between restarts
RESTART_BACKOFF_MULTIPLIER=2           # Exponential backoff multiplier
AUTO_RESTART_ENABLED=true              # Enable/disable automatic restarts

# Database connection pool configuration
DB_CONNECTION_LIMIT=20                 # Maximum number of connections in pool
DB_IDLE_TIMEOUT=60000                  # Connection idle timeout (ms)
```

### Retry Logic

The database connection retry logic works as follows:

1. **First attempt**: Immediate connection attempt
2. **Subsequent attempts**: Exponential backoff with 1.5x multiplier
   - Attempt 1: 0ms delay
   - Attempt 2: 2000ms delay
   - Attempt 3: 3000ms delay
   - Attempt 4: 4500ms delay
   - Attempt 5: 6750ms delay

### Error Types

The system distinguishes between different types of database errors:

- **Retryable errors**: `ETIMEDOUT`, `ECONNREFUSED`, `ENOTFOUND`
- **Non-retryable errors**: Authentication failures, invalid database names, etc.

Only retryable errors will trigger the retry mechanism.

## Monitoring

### Logs

The application logs detailed information about database connection attempts:

```
info: Database connection attempt 1/5
warn: Database connection attempt 1 failed (ETIMEDOUT), retrying in 2000ms...
info: Database connection attempt 2/5
info: Database connection successful
```

### Health Checks

The health monitor continuously checks database connectivity and will trigger restarts if:

- Database connections fail consistently
- Connection response times exceed thresholds
- Memory usage becomes excessive

### Restart Tracking

The restart manager tracks and logs:

- Number of restart attempts
- Reasons for restarts
- Time between restarts
- Success/failure of restart attempts

## Best Practices

1. **Monitor logs** for database connection issues
2. **Set appropriate timeouts** based on your database setup
3. **Configure restart limits** to prevent infinite restart loops
4. **Use health checks** to detect issues early
5. **Monitor resource usage** to identify underlying problems

## Troubleshooting

### Common Issues

1. **Database server unavailable**
   - Check database server status
   - Verify network connectivity
   - Review database server logs

2. **Connection timeout**
   - Increase `DB_STARTUP_RETRY_DELAY`
   - Check network latency to database
   - Review database connection limits

3. **Too many restarts**
   - Increase `RESTART_WINDOW_MS`
   - Reduce `MAX_RESTARTS` if needed
   - Investigate underlying database issues

### Debugging

Enable debug logging to see detailed connection information:

```bash
LOG_LEVEL=debug
```

This will show:
- Connection pool statistics
- Detailed error messages
- Retry attempt details
- Restart manager decisions
