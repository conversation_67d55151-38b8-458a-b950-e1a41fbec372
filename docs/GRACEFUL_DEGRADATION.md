# Graceful Degradation - Running Without Database

This document explains how the application can run in a degraded mode when the database is unavailable, providing better resilience and user experience.

## Overview

The application now supports two startup modes:

1. **Strict Mode** (`REQUIRE_DB_ON_STARTUP=true`) - Traditional behavior, server won't start without database
2. **Graceful Mode** (`REQUIRE_DB_ON_STARTUP=false`) - Server starts even without database, provides limited functionality

## Configuration

### Environment Variables

```bash
# Database connection behavior
REQUIRE_DB_ON_STARTUP=false           # Allow server to start without database
DB_RECONNECT_INTERVAL_MS=30000        # How often to retry database connection (30 seconds)

# Existing database retry settings still apply
DB_STARTUP_RETRIES=5
DB_STARTUP_RETRY_DELAY=2000
```

## How It Works

### Startup Sequence

1. **HTTP Server starts first** - Always accepts connections
2. **Database connection attempted** - With retry logic
3. **If database fails:**
   - **Strict mode**: Server shuts down and restart manager takes over
   - **Graceful mode**: Server continues running, starts background reconnection

### Background Reconnection

When running without database:
- Automatic reconnection attempts every 30 seconds (configurable)
- When database comes back online:
  - Database-dependent services restart automatically
  - Application returns to full functionality
  - No server restart required

### Service Management

**Always Available:**
- HTTP server and basic routes
- Health check endpoint
- Static file serving
- WebSocket connections (if not database-dependent)

**Database-Dependent (disabled when DB unavailable):**
- Competition refresher service
- Queue cleanup service
- Database-dependent API endpoints

## API Route Protection

Use middleware to handle database-dependent routes:

### Strict Database Requirement

```typescript
import { requireDatabase } from '../shared/middleware/database-guard';

// Route that absolutely needs database
router.get('/competitions', requireDatabase, async (req, res) => {
  // This will return 503 if database is unavailable
  const competitions = await dbConnect.query('SELECT * FROM competitions');
  res.json(competitions);
});
```

### Database-Aware Routes

```typescript
import { databaseAware, createDatabaseAwareResponse } from '../shared/middleware/database-guard';

// Route that can work with cached data or provide limited functionality
router.get('/status', databaseAware, async (req, res) => {
  const databaseAvailable = (req as any).databaseAvailable;
  
  if (databaseAvailable) {
    // Full functionality
    const liveData = await dbConnect.query('SELECT * FROM live_status');
    res.json(createDatabaseAwareResponse({ status: liveData }, true));
  } else {
    // Degraded functionality - return cached or static data
    const cachedData = { status: 'degraded', message: 'Using cached data' };
    res.json(createDatabaseAwareResponse(cachedData, false));
  }
});
```

## Health Check Response

The health endpoint now provides detailed database status:

```json
{
  "status": "degraded",
  "timestamp": "2025-07-14T10:30:00.000Z",
  "database": {
    "status": "disconnected",
    "connectionState": "disconnected", 
    "message": "Database is unavailable - running in degraded mode"
  },
  "uptime": 1234.5,
  "memory": { "rss": 50331648, "heapTotal": 20971520 }
}
```

## Error Responses

When database is required but unavailable:

```json
{
  "status": "error",
  "message": "Service temporarily unavailable - database connection required",
  "code": "DATABASE_UNAVAILABLE",
  "timestamp": "2025-07-14T10:30:00.000Z"
}
```

## Monitoring and Logging

### Log Messages

```
info: Server running on port 3000
warn: Database connection failed during startup: connect ETIMEDOUT
warn: Server starting without database connection - some features may be unavailable
info: Database reconnection loop started (interval: 30000ms)
info: Attempting database reconnection...
info: Database reconnection successful!
info: Restarting database-dependent services...
```

### Database State Tracking

The application tracks database connection state globally:
- `isDatabaseConnected` - Current connection status
- `getDatabaseConnectionState()` - Function to check status from other modules
- Background reconnection loop - Automatic recovery

## Best Practices

### Route Design

1. **Categorize your routes:**
   - **Critical**: Must have database (use `requireDatabase`)
   - **Flexible**: Can work with cached data (use `databaseAware`)
   - **Independent**: Don't need database at all

2. **Implement caching:**
   - Cache frequently accessed data
   - Serve cached data when database unavailable
   - Update cache when database reconnects

3. **User communication:**
   - Clear error messages about service availability
   - Status indicators in UI
   - Graceful fallbacks for missing features

### Deployment Strategy

1. **Development**: Use `REQUIRE_DB_ON_STARTUP=false` for easier testing
2. **Production**: Choose based on your requirements:
   - **High availability**: `false` - Better uptime, some features may be limited
   - **Data consistency**: `true` - Ensures all features work or none do

### Monitoring

1. **Set up alerts** for database disconnection events
2. **Monitor health endpoint** for service status
3. **Track reconnection success/failure rates**
4. **Monitor user impact** during degraded mode

## Migration from Strict Mode

If you're currently using strict mode and want to enable graceful degradation:

1. **Set environment variable**: `REQUIRE_DB_ON_STARTUP=false`
2. **Review your API routes** - add appropriate middleware
3. **Implement caching** for critical data
4. **Update monitoring** to handle degraded state
5. **Test thoroughly** with database unavailable

## Troubleshooting

### Server won't start even in graceful mode
- Check if `REQUIRE_DB_ON_STARTUP=true` is set
- Verify HTTP server port is available
- Check application logs for startup errors

### Database never reconnects
- Verify database server is accessible
- Check `DB_RECONNECT_INTERVAL_MS` setting
- Review database connection configuration
- Check network connectivity and firewall rules

### Some features still fail without database
- Ensure routes use appropriate middleware
- Check for direct database calls without protection
- Review service startup logic
- Add error handling for database operations
