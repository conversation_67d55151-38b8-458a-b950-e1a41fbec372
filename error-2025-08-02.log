{"date":"Sat Aug 02 2025 01:13:28 GMT+0000 (Coordinated Universal Time)","error":{"code":"EPIPE","errno":-32,"syscall":"write"},"exception":true,"level":"error","message":"uncaughtException: write EPIPE\nError: write EPIPE\n    at afterWriteDispatched (node:internal/stream_base_commons:159:15)\n    at writeGeneric (node:internal/stream_base_commons:150:3)\n    at Socket._writeGeneric (node:net:966:11)\n    at Socket._write (node:net:978:8)\n    at writeOrBuffer (node:internal/streams/writable:572:12)\n    at _write (node:internal/streams/writable:501:10)\n    at Writable.write (node:internal/streams/writable:510:10)\n    at Console.log (/var/www/vhosts/live.entry4sports.co.uk/httpdocs/node_modules/winston/lib/winston/transports/console.js:87:23)\n    at Console._write (/var/www/vhosts/live.entry4sports.co.uk/httpdocs/node_modules/winston-transport/modern.js:103:17)\n    at doWrite (/var/www/vhosts/live.entry4sports.co.uk/httpdocs/node_modules/readable-stream/lib/_stream_writable.js:390:139)","os":{"loadavg":[0,0,0],"uptime":217543.7},"process":{"argv":["/opt/plesk/node/22/bin/node","/var/www/vhosts/live.entry4sports.co.uk/httpdocs/index.js"],"cwd":"/var/www/vhosts/live.entry4sports.co.uk/httpdocs","execPath":"/opt/plesk/node/22/bin/node","gid":1003,"memoryUsage":{"arrayBuffers":2418729,"external":5929052,"heapTotal":115658752,"heapUsed":74056272,"rss":199659520},"pid":2396,"uid":10000,"version":"v22.17.1"},"stack":"Error: write EPIPE\n    at afterWriteDispatched (node:internal/stream_base_commons:159:15)\n    at writeGeneric (node:internal/stream_base_commons:150:3)\n    at Socket._writeGeneric (node:net:966:11)\n    at Socket._write (node:net:978:8)\n    at writeOrBuffer (node:internal/streams/writable:572:12)\n    at _write (node:internal/streams/writable:501:10)\n    at Writable.write (node:internal/streams/writable:510:10)\n    at Console.log (/var/www/vhosts/live.entry4sports.co.uk/httpdocs/node_modules/winston/lib/winston/transports/console.js:87:23)\n    at Console._write (/var/www/vhosts/live.entry4sports.co.uk/httpdocs/node_modules/winston-transport/modern.js:103:17)\n    at doWrite (/var/www/vhosts/live.entry4sports.co.uk/httpdocs/node_modules/readable-stream/lib/_stream_writable.js:390:139)","timestamp":"2025-08-02T01:13:28.880Z","trace":[{"column":15,"file":"node:internal/stream_base_commons","function":"afterWriteDispatched","line":159,"method":null,"native":false},{"column":3,"file":"node:internal/stream_base_commons","function":"writeGeneric","line":150,"method":null,"native":false},{"column":11,"file":"node:net","function":"Socket._writeGeneric","line":966,"method":"_writeGeneric","native":false},{"column":8,"file":"node:net","function":"Socket._write","line":978,"method":"_write","native":false},{"column":12,"file":"node:internal/streams/writable","function":"writeOrBuffer","line":572,"method":null,"native":false},{"column":10,"file":"node:internal/streams/writable","function":"_write","line":501,"method":null,"native":false},{"column":10,"file":"node:internal/streams/writable","function":"Writable.write","line":510,"method":"write","native":false},{"column":23,"file":"/var/www/vhosts/live.entry4sports.co.uk/httpdocs/node_modules/winston/lib/winston/transports/console.js","function":"Console.log","line":87,"method":"log","native":false},{"column":17,"file":"/var/www/vhosts/live.entry4sports.co.uk/httpdocs/node_modules/winston-transport/modern.js","function":"Console._write","line":103,"method":"_write","native":false},{"column":139,"file":"/var/www/vhosts/live.entry4sports.co.uk/httpdocs/node_modules/readable-stream/lib/_stream_writable.js","function":"doWrite","line":390,"method":null,"native":false}]}
{"date":"Sat Aug 02 2025 01:13:28 GMT+0000 (Coordinated Universal Time)","error":{"code":"EPIPE","errno":-32,"syscall":"write"},"exception":true,"level":"error","message":"uncaughtException: write EPIPE\nError: write EPIPE\n    at afterWriteDispatched (node:internal/stream_base_commons:159:15)\n    at writeGeneric (node:internal/stream_base_commons:150:3)\n    at Socket._writeGeneric (node:net:966:11)\n    at Socket._write (node:net:978:8)\n    at writeOrBuffer (node:internal/streams/writable:572:12)\n    at _write (node:internal/streams/writable:501:10)\n    at Writable.write (node:internal/streams/writable:510:10)\n    at Console.log (/var/www/vhosts/live.entry4sports.co.uk/httpdocs/node_modules/winston/lib/winston/transports/console.js:87:23)\n    at Console._write (/var/www/vhosts/live.entry4sports.co.uk/httpdocs/node_modules/winston-transport/modern.js:103:17)\n    at doWrite (/var/www/vhosts/live.entry4sports.co.uk/httpdocs/node_modules/readable-stream/lib/_stream_writable.js:390:139)","os":{"loadavg":[0,0,0],"uptime":217543.7},"process":{"argv":["/opt/plesk/node/22/bin/node","/var/www/vhosts/live.entry4sports.co.uk/httpdocs/index.js"],"cwd":"/var/www/vhosts/live.entry4sports.co.uk/httpdocs","execPath":"/opt/plesk/node/22/bin/node","gid":1003,"memoryUsage":{"arrayBuffers":2418729,"external":5929052,"heapTotal":115658752,"heapUsed":74056272,"rss":199659520},"pid":2396,"uid":10000,"version":"v22.17.1"},"stack":"Error: write EPIPE\n    at afterWriteDispatched (node:internal/stream_base_commons:159:15)\n    at writeGeneric (node:internal/stream_base_commons:150:3)\n    at Socket._writeGeneric (node:net:966:11)\n    at Socket._write (node:net:978:8)\n    at writeOrBuffer (node:internal/streams/writable:572:12)\n    at _write (node:internal/streams/writable:501:10)\n    at Writable.write (node:internal/streams/writable:510:10)\n    at Console.log (/var/www/vhosts/live.entry4sports.co.uk/httpdocs/node_modules/winston/lib/winston/transports/console.js:87:23)\n    at Console._write (/var/www/vhosts/live.entry4sports.co.uk/httpdocs/node_modules/winston-transport/modern.js:103:17)\n    at doWrite (/var/www/vhosts/live.entry4sports.co.uk/httpdocs/node_modules/readable-stream/lib/_stream_writable.js:390:139)","timestamp":"2025-08-02T01:13:28.880Z","trace":[{"column":15,"file":"node:internal/stream_base_commons","function":"afterWriteDispatched","line":159,"method":null,"native":false},{"column":3,"file":"node:internal/stream_base_commons","function":"writeGeneric","line":150,"method":null,"native":false},{"column":11,"file":"node:net","function":"Socket._writeGeneric","line":966,"method":"_writeGeneric","native":false},{"column":8,"file":"node:net","function":"Socket._write","line":978,"method":"_write","native":false},{"column":12,"file":"node:internal/streams/writable","function":"writeOrBuffer","line":572,"method":null,"native":false},{"column":10,"file":"node:internal/streams/writable","function":"_write","line":501,"method":null,"native":false},{"column":10,"file":"node:internal/streams/writable","function":"Writable.write","line":510,"method":"write","native":false},{"column":23,"file":"/var/www/vhosts/live.entry4sports.co.uk/httpdocs/node_modules/winston/lib/winston/transports/console.js","function":"Console.log","line":87,"method":"log","native":false},{"column":17,"file":"/var/www/vhosts/live.entry4sports.co.uk/httpdocs/node_modules/winston-transport/modern.js","function":"Console._write","line":103,"method":"_write","native":false},{"column":139,"file":"/var/www/vhosts/live.entry4sports.co.uk/httpdocs/node_modules/readable-stream/lib/_stream_writable.js","function":"doWrite","line":390,"method":null,"native":false}]}
