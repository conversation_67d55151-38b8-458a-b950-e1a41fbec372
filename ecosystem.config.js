module.exports = {
  apps: [
    {
      name: 'liteserver',
      script: './dist/index.js',
      instances: 1, // Single instance for your use case
      exec_mode: 'fork', // Use fork mode for single instance
      
      // Auto-restart configuration
      autorestart: true,
      max_restarts: 10, // Maximum number of restarts
      min_uptime: '10s', // Minimum uptime before considering restart successful
      max_memory_restart: '512M', // Restart if memory usage exceeds this
      
      // Restart strategies
      restart_delay: 4000, // Delay between restarts (4 seconds)
      exp_backoff_restart_delay: 100, // Exponential backoff starting delay
      
      // Environment configuration
      env: {
        NODE_ENV: 'development',
        APP_PORT: 3000,
        // Message Queue Configuration for development
        MESSAGE_QUEUE_MAX_SIZE: '500', // Smaller limit for development
        MESSAGE_QUEUE_MAX_AGE_MS: '1800000', // 30 minutes
        MESSAGE_QUEUE_WARNING_THRESHOLD: '400',
        QUEUE_CLEANUP_INTERVAL_MS: '180000', // 3 minutes for development
      },
      env_production: {
        NODE_ENV: 'production',
        APP_PORT: 8765,
        // Auto-restart settings for production
        AUTO_RESTART_ENABLED: 'true',
        MAX_RESTARTS: '5',
        RESTART_WINDOW_MS: '300000', // 5 minutes
        RESTART_INITIAL_DELAY_MS: '2000', // 2 seconds
        RESTART_MAX_DELAY_MS: '60000', // 1 minute
        RESTART_BACKOFF_MULTIPLIER: '2',
        
        // Health monitoring settings
        HEALTH_MONITOR_ENABLED: 'true',
        HEALTH_CHECK_INTERVAL_MS: '30000', // 30 seconds
        HEALTH_UNHEALTHY_THRESHOLD: '3', // 3 consecutive failures
        HEALTH_MEMORY_THRESHOLD_MB: '400', // 400 MB threshold
        
        // Logging settings
        ENABLE_CONSOLE_LOGGING: 'false',
        LOG_LEVEL: 'info',

        // Message Queue Configuration for production
        MESSAGE_QUEUE_MAX_SIZE: '1000',
        MESSAGE_QUEUE_MAX_AGE_MS: '3600000', // 1 hour
        MESSAGE_QUEUE_WARNING_THRESHOLD: '800',
        QUEUE_CLEANUP_INTERVAL_MS: '300000', // 5 minutes
      },
      
      // Logging configuration
      log_file: './logs/pm2-combined.log',
      out_file: './logs/pm2-out.log',
      error_file: './logs/pm2-error.log',
      log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
      merge_logs: true,
      
      // Process monitoring
      watch: false, // Disable file watching in production
      ignore_watch: ['node_modules', 'logs', 'dist'],
      
      // Advanced restart conditions
      kill_timeout: 5000, // Time to wait before force killing
      listen_timeout: 3000, // Time to wait for app to listen
      
      // Cron restart (optional - restart daily at 3 AM)
      cron_restart: '0 3 * * *',
      
      // Source map support
      source_map_support: true,
      
      // Instance variables
      instance_var: 'INSTANCE_ID',
      
      // Health check endpoint (if you add one)
      // health_check_url: 'http://localhost:8765/health',
      // health_check_grace_period: 3000,
    }
  ],

  // Deployment configuration (optional)
  deploy: {
    production: {
      user: 'node',
      host: 'your-server.com',
      ref: 'origin/main',
      repo: '**************:your-repo/liteserver.git',
      path: '/var/www/liteserver',
      'pre-deploy-local': '',
      'post-deploy': 'npm install && npm run build && pm2 reload ecosystem.config.js --env production',
      'pre-setup': ''
    }
  }
};
