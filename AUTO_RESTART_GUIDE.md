# Auto-Restart Strategy Guide

## Overview

This guide outlines the comprehensive auto-restart strategies implemented for the LiteServer application to ensure maximum uptime and reliability in production environments.

## Multi-Layer Restart Protection

### 1. Application-Level Restart Manager

**Location**: `src/shared/lib/restart-manager.ts`

**Features**:
- Exponential backoff restart delays
- Maximum restart limits within time windows
- Graceful cleanup before restart
- Configurable restart policies

**Configuration** (Environment Variables):
```bash
AUTO_RESTART_ENABLED=true          # Enable/disable auto-restart
MAX_RESTARTS=5                     # Max restarts in time window
RESTART_WINDOW_MS=300000           # Time window (5 minutes)
RESTART_INITIAL_DELAY_MS=2000      # Initial delay (2 seconds)
RESTART_MAX_DELAY_MS=60000         # Maximum delay (1 minute)
RESTART_BACKOFF_MULTIPLIER=2       # Exponential backoff multiplier
```

**Restart Triggers**:
- Uncaught exceptions (except EPIPE)
- Unhandled promise rejections
- Health check failures
- Manual restart requests

### 2. Health Monitoring System

**Location**: `src/shared/lib/health-monitor.ts`

**Features**:
- Database connectivity checks
- Memory usage monitoring
- Event loop lag detection
- Process uptime tracking
- Automatic restart on consecutive failures

**Configuration**:
```bash
HEALTH_MONITOR_ENABLED=true        # Enable health monitoring
HEALTH_CHECK_INTERVAL_MS=30000     # Check every 30 seconds
HEALTH_UNHEALTHY_THRESHOLD=3       # 3 consecutive failures trigger restart
HEALTH_MEMORY_THRESHOLD_MB=400     # Memory threshold (400 MB)
```

**Health Check Endpoint**: `GET /health`

### 3. PM2 Process Manager (Recommended)

**Configuration File**: `ecosystem.config.js`

**Features**:
- Automatic restart on crashes
- Memory-based restart limits
- Exponential backoff delays
- Log management
- Cluster mode support
- Cron-based restarts

**Key Settings**:
```javascript
{
  autorestart: true,
  max_restarts: 10,
  min_uptime: '10s',
  max_memory_restart: '512M',
  restart_delay: 4000,
  exp_backoff_restart_delay: 100
}
```

**Commands**:
```bash
# Install PM2 globally
npm install -g pm2

# Start application
pm2 start ecosystem.config.js --env production

# Monitor
pm2 status
pm2 logs liteserver
pm2 monit

# Restart
pm2 restart liteserver

# Setup auto-start on boot
pm2 startup
pm2 save
```

### 4. Systemd Service (Linux Systems)

**Service File**: `liteserver.service`

**Features**:
- System-level process management
- Automatic restart on failure
- Resource limits
- Security constraints
- Journal logging

**Installation**:
```bash
# Copy service file
sudo cp liteserver.service /etc/systemd/system/

# Enable and start
sudo systemctl daemon-reload
sudo systemctl enable liteserver
sudo systemctl start liteserver

# Monitor
sudo systemctl status liteserver
sudo journalctl -u liteserver -f
```

## Restart Strategies by Scenario

### 1. Application Crashes

**Strategy**: Multi-layer protection
1. **Application-level**: Catches uncaught exceptions, attempts graceful restart
2. **PM2/systemd**: Immediately restarts if process dies
3. **Health monitor**: Detects unresponsive application, triggers restart

### 2. Memory Leaks

**Strategy**: Proactive memory monitoring
1. **Health monitor**: Tracks memory usage, triggers restart at threshold
2. **PM2**: Automatic restart when memory limit exceeded
3. **Systemd**: Resource limits prevent system impact

### 3. Database Connection Issues

**Strategy**: Health-based restart
1. **Health monitor**: Detects database connectivity issues
2. **Automatic restart**: After consecutive health check failures
3. **Exponential backoff**: Prevents restart loops

### 4. High Load/Event Loop Lag

**Strategy**: Performance monitoring
1. **Health monitor**: Detects event loop lag
2. **Graceful restart**: When performance degrades significantly
3. **Load balancing**: Multiple instances (if using cluster mode)

## Deployment Options

### Option 1: PM2 (Recommended for IONOS Plesk)

```bash
# Install PM2
npm install -g pm2

# Deploy
npm run build
pm2 start ecosystem.config.js --env production
pm2 save
pm2 startup
```

**Pros**:
- Easy to use with Plesk
- Excellent monitoring
- Built-in log management
- Web dashboard available

### Option 2: Systemd (Linux VPS)

```bash
# Deploy with systemd
sudo cp liteserver.service /etc/systemd/system/
sudo systemctl enable liteserver
sudo systemctl start liteserver
```

**Pros**:
- System-level integration
- Better security
- Resource management
- Boot-time startup

### Option 3: Docker with Restart Policies

```dockerfile
# In Dockerfile
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:8765/health || exit 1

# Run with restart policy
docker run -d --restart=unless-stopped --name liteserver liteserver:latest
```

## Monitoring and Alerting

### 1. Log Monitoring

**Files to Monitor**:
- `logs/error-*.log` - Application errors
- `logs/pm2-error.log` - PM2 errors (if using PM2)
- System logs via `journalctl` (if using systemd)

### 2. Health Check Monitoring

**Endpoint**: `GET /health`

**External Monitoring Tools**:
- UptimeRobot
- Pingdom
- StatusCake
- Custom monitoring scripts

### 3. Restart Alerts

**Set up alerts for**:
- Multiple restarts in short time
- Health check failures
- Memory threshold breaches
- Database connectivity issues

## Best Practices

### 1. Restart Limits

- **Don't restart indefinitely**: Set maximum restart limits
- **Use time windows**: Reset counters after stable operation
- **Exponential backoff**: Prevent restart storms

### 2. Graceful Shutdown

- **Clean up resources**: Close database connections, stop timers
- **Save state**: Persist important data before restart
- **Signal handling**: Respond to SIGTERM/SIGINT properly

### 3. Health Checks

- **Multiple checks**: Database, memory, performance
- **Reasonable thresholds**: Not too sensitive, not too lenient
- **Quick checks**: Don't block the application

### 4. Logging

- **Structured logging**: Use JSON format for easy parsing
- **Log rotation**: Prevent disk space issues
- **Error context**: Include enough information for debugging

## Troubleshooting

### Restart Loops

**Symptoms**: Application keeps restarting
**Solutions**:
1. Check application logs for root cause
2. Increase restart delays
3. Reduce restart limits temporarily
4. Fix underlying issues

### Memory Issues

**Symptoms**: Frequent memory-based restarts
**Solutions**:
1. Investigate memory leaks
2. Increase memory threshold temporarily
3. Optimize application code
4. Consider horizontal scaling

### Database Connection Problems

**Symptoms**: Health checks failing on database
**Solutions**:
1. Check database server status
2. Verify connection credentials
3. Increase connection timeout
4. Implement connection pooling

## Quick Start

1. **Build and configure**:
   ```bash
   npm run build
   cp .env.production .env
   ```

2. **Choose deployment method**:
   - PM2: `pm2 start ecosystem.config.js --env production`
   - systemd: `sudo systemctl start liteserver`
   - Manual: `node dist/index.js`

3. **Verify health**:
   ```bash
   curl http://localhost:8765/health
   ```

4. **Monitor logs**:
   ```bash
   # PM2
   pm2 logs liteserver
   
   # systemd
   sudo journalctl -u liteserver -f
   
   # Manual
   tail -f logs/combined-*.log
   ```

The auto-restart system is now ready to keep your application running reliably in production!
