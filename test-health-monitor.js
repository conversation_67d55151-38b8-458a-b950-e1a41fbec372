#!/usr/bin/env node

// Test script to verify health monitor functionality
// Run with: node test-health-monitor.js

// Set test environment
process.env.NODE_ENV = 'development';
process.env.HEALTH_MONITOR_ENABLED = 'true';
process.env.HEALTH_CHECK_INTERVAL_MS = '5000'; // 5 seconds for testing
process.env.HEALTH_UNHEALTHY_THRESHOLD = '2'; // 2 failures for testing
process.env.HEALTH_MEMORY_THRESHOLD_MB = '100'; // Low threshold for testing

console.log('Testing Health Monitor...');
console.log('Environment:', {
  NODE_ENV: process.env.NODE_ENV,
  HEALTH_MONITOR_ENABLED: process.env.HEALTH_MONITOR_ENABLED,
  HEALTH_CHECK_INTERVAL_MS: process.env.HEALTH_CHECK_INTERVAL_MS,
  HEALTH_UNHEALTHY_THRESHOLD: process.env.HEALTH_UNHEALTHY_THRESHOLD,
});

async function testHealthMonitor() {
  try {
    // Import the health monitor
    const { healthMonitor } = require('./dist/shared/lib/health-monitor');
    
    console.log('\n1. Testing health monitor initialization...');
    const initialStatus = healthMonitor.getStatus();
    console.log('Initial status:', initialStatus);
    
    console.log('\n2. Starting health monitor...');
    healthMonitor.start();
    
    // Wait for a few health checks
    console.log('\n3. Waiting for health checks to run...');
    await new Promise(resolve => setTimeout(resolve, 12000)); // Wait 12 seconds
    
    console.log('\n4. Checking status after health checks...');
    const runningStatus = healthMonitor.getStatus();
    console.log('Running status:', runningStatus);
    
    console.log('\n5. Stopping health monitor...');
    healthMonitor.stop();
    
    const stoppedStatus = healthMonitor.getStatus();
    console.log('Stopped status:', stoppedStatus);
    
    console.log('\n✅ Health monitor test completed successfully!');
    
  } catch (error) {
    console.error('❌ Test failed:', error);
    
    // If modules aren't built yet, provide helpful message
    if (error.code === 'MODULE_NOT_FOUND') {
      console.log('\n💡 Tip: Make sure to build the project first:');
      console.log('   npm run build');
      console.log('   Then run this test again.');
    }
    
    process.exit(1);
  }
}

// Mock database connection for testing
const mockDbConnect = {
  testConnection: async () => {
    console.log('🔍 Mock database connection test - OK');
    return true;
  },
  close: async () => {
    console.log('🔒 Mock database connection closed');
  }
};

// Mock restart manager for testing
const mockRestartManager = {
  attemptRestart: async (reason) => {
    console.log(`🔄 Mock restart attempt: ${reason}`);
    return false; // Don't actually restart during testing
  }
};

// Run the test
testHealthMonitor().then(() => {
  console.log('\nTest completed successfully!');
  process.exit(0);
}).catch(error => {
  console.error('Test error:', error);
  process.exit(1);
});
