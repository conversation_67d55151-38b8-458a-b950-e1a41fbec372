#!/usr/bin/env node

/**
 * Test script to verify EPIPE error fixes are working correctly
 * This simulates the conditions that caused the original EPIPE crash
 */

// Set production environment to test the fix
process.env.NODE_ENV = 'production';
process.env.ENABLE_CONSOLE_LOGGING = 'false';

console.log('🧪 Testing EPIPE Error Fixes');
console.log('Environment: production');
console.log('Console logging: disabled');
console.log('');

// Import the safe logger
const { logger } = require('./dist/shared/lib/logger');

let testsPassed = 0;
let testsTotal = 0;

function runTest(testName, testFn) {
  testsTotal++;
  console.log(`Running: ${testName}`);
  
  try {
    testFn();
    testsPassed++;
    console.log(`✅ PASS: ${testName}`);
  } catch (error) {
    console.log(`❌ FAIL: ${testName} - ${error.message}`);
  }
  console.log('');
}

// Test 1: Basic logging should work without console output
runTest('Basic file-only logging', () => {
  logger.info('Test info message');
  logger.warn('Test warning message');
  logger.error('Test error message');
  logger.debug('Test debug message');
});

// Test 2: Logging with metadata
runTest('Logging with metadata', () => {
  logger.info('Test message with metadata', { 
    userId: 123, 
    action: 'test',
    timestamp: new Date().toISOString()
  });
});

// Test 3: Error logging
runTest('Error object logging', () => {
  try {
    throw new Error('Test error for logging');
  } catch (error) {
    logger.error('Caught test error:', {
      message: error.message,
      stack: error.stack
    });
  }
});

// Test 4: Emergency logging
runTest('Emergency logging system', () => {
  logger.emergency('Emergency test message', { 
    reason: 'Testing emergency logging system',
    timestamp: new Date().toISOString()
  });
});

// Test 5: Rapid logging (stress test)
runTest('Rapid logging stress test', () => {
  for (let i = 0; i < 50; i++) {
    logger.info(`Rapid log message ${i}`, { iteration: i });
  }
});

// Test 6: Simulate EPIPE error handling
runTest('EPIPE error simulation', () => {
  // Simulate the original error condition by creating a mock error
  const epipeError = new Error('write EPIPE');
  epipeError.code = 'EPIPE';
  epipeError.errno = -32;
  epipeError.syscall = 'write';
  
  // This should be handled gracefully by the safe logger
  try {
    // Simulate what would happen in the original error scenario
    logger.error('Simulated EPIPE error handling', {
      error: epipeError.message,
      code: epipeError.code
    });
  } catch (error) {
    // This should not happen with the fix
    throw new Error('EPIPE error was not handled gracefully');
  }
});

// Test 7: Multiple error types
runTest('Multiple error type handling', () => {
  const errorTypes = ['EPIPE', 'ENOTCONN', 'ECONNRESET'];
  
  errorTypes.forEach(errorCode => {
    const mockError = new Error(`Test ${errorCode} error`);
    mockError.code = errorCode;
    
    logger.error(`Handling ${errorCode} error`, {
      error: mockError.message,
      code: mockError.code
    });
  });
});

// Test 8: Verify no console transport in production
runTest('Console transport disabled in production', () => {
  const winston = require('winston');
  const loggerInstance = logger._original;
  
  // Check if console transport exists
  const hasConsoleTransport = loggerInstance.transports.some(
    transport => transport.constructor.name === 'Console'
  );
  
  if (hasConsoleTransport) {
    throw new Error('Console transport found in production environment');
  }
});

// Wait a moment for async operations to complete
setTimeout(() => {
  console.log('='.repeat(50));
  console.log(`Test Results: ${testsPassed}/${testsTotal} tests passed`);
  
  if (testsPassed === testsTotal) {
    console.log('🎉 All EPIPE fixes are working correctly!');
    console.log('');
    console.log('✅ Console transport disabled in production');
    console.log('✅ Safe logger wrapper handling errors');
    console.log('✅ Emergency logging system functional');
    console.log('✅ No console output in error handlers');
    console.log('');
    console.log('📁 Check the logs directory for output files:');
    console.log('   - logs/combined-YYYY-MM-DD.log');
    console.log('   - logs/error-YYYY-MM-DD.log');
    console.log('   - logs/emergency.log (if emergency logging was used)');
    console.log('   - logs/transport-errors.log (if transport errors occurred)');
    console.log('   - logs/logger-errors.log (if logger errors occurred)');
  } else {
    console.log('❌ Some tests failed. Check the implementation.');
    process.exit(1);
  }
  
  process.exit(0);
}, 2000);
