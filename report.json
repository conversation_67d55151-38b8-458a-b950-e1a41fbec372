{"aggregate": {"counters": {"vusers.created_by_name.Socket.IO connections": 9, "vusers.created": 9, "errors.Error: websocket error": 9, "vusers.failed": 9}, "rates": {}, "firstCounterAt": 1751354485203, "lastCounterAt": 1751354490243, "firstMetricAt": 1751354485203, "lastMetricAt": 1751354490243, "period": 1751354490000, "summaries": {}, "histograms": {}}, "intermediate": [{"counters": {"vusers.created_by_name.Socket.IO connections": 7, "vusers.created": 7, "errors.Error: websocket error": 7, "vusers.failed": 7}, "rates": {}, "firstCounterAt": 1751354485203, "lastCounterAt": 1751354489224, "firstMetricAt": 1751354485203, "lastMetricAt": 1751354489224, "period": "1751354480000", "summaries": {}, "histograms": {}}, {"counters": {"vusers.created_by_name.Socket.IO connections": 2, "vusers.created": 2, "errors.Error: websocket error": 2, "vusers.failed": 2}, "rates": {}, "firstCounterAt": 1751354490225, "lastCounterAt": 1751354490243, "firstMetricAt": 1751354490225, "lastMetricAt": 1751354490243, "period": "1751354490000", "summaries": {}, "histograms": {}}]}