# Authentication & Authorization Logging

This document describes the comprehensive logging system implemented for authentication and authorization failures in the application.

## Overview

When users are denied access to restricted routes, the system now logs detailed information about why the access was denied. This helps with debugging, security monitoring, and understanding user access patterns.

## What Gets Logged

### User Context Information
- **User ID**: The unique identifier of the user (or 'unknown' if not authenticated)
- **Email**: The user's email address (or 'unknown' if not available)
- **Roles**: Array of roles assigned to the user
- **IP Address**: The client's IP address
- **User Agent**: The browser/client information
- **Route**: The URL path that was accessed
- **HTTP Method**: GET, POST, PUT, DELETE, etc.

### Access Denial Reasons
The system logs specific reasons for access denial:

1. **`no_authentication`**: User is not authenticated (no valid token)
2. **`no_token`**: No authentication token provided
3. **`invalid_token`**: Authentication token is invalid or expired
4. **`authentication_required`**: Route requires authentication but user is not authenticated
5. **`no_roles_assigned`**: User has no roles assigned
6. **`insufficient_role`**: User doesn't have the required role
7. **`insufficient_permissions`**: User lacks the required permission
8. **`insufficient_permissions_any`**: User lacks any of the required permissions
9. **`insufficient_permissions_all`**: User lacks some of the required permissions

### Permission Details
- **Required Role/Permission**: What role or permission was needed
- **User's Actual Roles/Permissions**: What the user actually has
- **Missing Permissions**: Specific permissions the user is missing (for `hasAllPermissions`)

## Log Examples

### Authentication Failure
```json
{
  "level": "warn",
  "message": "Authentication failed: No token provided",
  "timestamp": "2025-07-13T10:30:00.000Z",
  "ip": "*************",
  "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
  "route": "/admin/users",
  "method": "GET",
  "reason": "no_token"
}
```

### Role-Based Access Denial
```json
{
  "level": "warn",
  "message": "Access denied: Insufficient role",
  "timestamp": "2025-07-13T10:30:00.000Z",
  "userId": "user-123",
  "email": "<EMAIL>",
  "roles": ["user"],
  "ip": "*************",
  "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
  "route": "/admin/dashboard",
  "method": "GET",
  "reason": "insufficient_role",
  "requiredRole": "admin",
  "userRoles": ["user"]
}
```

### Permission-Based Access Denial
```json
{
  "level": "warn",
  "message": "Access denied: Insufficient permissions",
  "timestamp": "2025-07-13T10:30:00.000Z",
  "userId": "user-123",
  "email": "<EMAIL>",
  "roles": ["user"],
  "ip": "*************",
  "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
  "route": "/admin/users",
  "method": "POST",
  "reason": "insufficient_permissions",
  "requiredPermission": "manage:users",
  "userPermissions": ["view:competitions", "view:entries"]
}
```

### Multiple Permissions Denial
```json
{
  "level": "warn",
  "message": "Access denied: User lacks some required permissions",
  "timestamp": "2025-07-13T10:30:00.000Z",
  "userId": "editor-456",
  "email": "<EMAIL>",
  "roles": ["editor"],
  "ip": "*************",
  "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
  "route": "/competitions/delete",
  "method": "DELETE",
  "reason": "insufficient_permissions_all",
  "requiredPermissions": ["view:competitions", "edit:competition", "delete:competition"],
  "userPermissions": ["view:competitions", "edit:competition"],
  "missingPermissions": ["delete:competition"]
}
```

## Log Locations

Logs are written to the following locations:
- **Console**: In development mode (when `NODE_ENV !== 'production'` or `ENABLE_CONSOLE_LOGGING=true`)
- **File**: `logs/combined-YYYY-MM-DD.log` (all logs)
- **Error File**: `logs/error-YYYY-MM-DD.log` (error level logs only)

## Middleware Functions Enhanced

The following middleware functions now include comprehensive logging:

### Authentication Middleware (`src/features/auth/middleware/auth.ts`)
- `verifyToken()`: Logs invalid tokens and missing tokens
- `requireAuth()`: Logs when authentication is required but missing

### Authorization Middleware (`src/features/auth/middleware/rbac.ts`)
- `hasRole()`: Logs role-based access denials
- `hasPermission()`: Logs permission-based access denials
- `hasAnyPermission()`: Logs when user lacks any required permissions
- `hasAllPermissions()`: Logs when user lacks some required permissions

## Testing the Logging

### Running Tests
```bash
npm test
```

### Demo Server
A demo server is available to test the logging functionality:

```bash
# Run the demo server
ts-node src/demo-auth-logging.ts

# Visit http://localhost:3001/demo to see available test scenarios
```

### Manual Testing
You can test the logging by:
1. Making requests to protected routes without authentication
2. Using tokens with insufficient permissions
3. Accessing admin routes with user-level accounts

## Security Considerations

- **No Sensitive Data**: Logs do not include passwords, tokens, or other sensitive information
- **IP Tracking**: IP addresses are logged for security monitoring
- **Log Rotation**: Logs are automatically rotated daily and compressed
- **Access Control**: Ensure log files have appropriate file permissions

## Monitoring and Alerting

Consider setting up monitoring for:
- High frequency of access denials from specific IPs
- Repeated authentication failures
- Unusual access patterns
- Failed admin access attempts

## Configuration

Logging behavior can be configured via environment variables:
- `LOG_LEVEL`: Set logging level (debug, info, warn, error)
- `ENABLE_CONSOLE_LOGGING`: Enable console logging in production
- `NODE_ENV`: Controls default logging behavior
