# Project To-Do List

## High Priority
- [ ] Store socket messages in queue, from last cache.  But server get comp ...if active...every 5 mins?
- [ ] Shared interfaces between liteserver and lite projects.
- [ ] Server health: CPU, memory, etc.
- [ ] Zod.
- [ ] Split socket models.
- [ ] Authentication
- [ ] Socket server.
- [ ] Load testing.  artillery run --output report.json load-tests/artillery-config.yml
      npx artillery run --output report.json load-tests/artillery-config.yml
- [ ] Socket state persistence, E.g. Redis

## Features
- [ ] socket messages: seed-entries, seed-confirmed, athlete-bibNo, 
- track-move, seed-dropped, entries-present
- [ ] Feature 2

## Bugs
- [ ] scrape in progress not working.
- [ ] WordPress authentication

## Technical Debt
- [ ] Refactor X
- [ ] Improve Y

## Notes
- [ ] https://console.cloud.google.com/apis/credentials
- [ ] Item 2
