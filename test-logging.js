#!/usr/bin/env node

// Test script to verify logging configuration works without EPIPE errors
// Run with: node test-logging.js

// Set production environment
process.env.NODE_ENV = 'production';
process.env.ENABLE_CONSOLE_LOGGING = 'false';

// Import the logger
const { logger } = require('./dist/shared/lib/logger');

console.log('Testing logging configuration...');
console.log('Environment:', process.env.NODE_ENV);
console.log('Console logging enabled:', process.env.ENABLE_CONSOLE_LOGGING);

// Test different log levels
logger.info('Test info message');
logger.warn('Test warning message');
logger.error('Test error message');
logger.debug('Test debug message');

// Test logging with metadata
logger.info('Test message with metadata', { 
  userId: 123, 
  action: 'test',
  timestamp: new Date().toISOString()
});

// Test error logging
try {
  throw new Error('Test error for logging');
} catch (error) {
  logger.error('Caught test error:', error);
}

console.log('Logging test completed. Check logs directory for output files.');

// Test EPIPE handling by simulating a broken pipe
setTimeout(() => {
  console.log('Testing EPIPE error handling...');
  
  // Simulate multiple rapid log calls that might trigger EPIPE
  for (let i = 0; i < 100; i++) {
    logger.info(`Rapid log message ${i}`);
  }
  
  console.log('EPIPE test completed.');
  process.exit(0);
}, 1000);
