#!/usr/bin/env node

// Test script to verify restart manager functionality
// Run with: node test-restart-manager.js

// Set test environment
process.env.NODE_ENV = 'development';
process.env.AUTO_RESTART_ENABLED = 'true';
process.env.MAX_RESTARTS = '3';
process.env.RESTART_WINDOW_MS = '60000'; // 1 minute
process.env.RESTART_INITIAL_DELAY_MS = '1000'; // 1 second
process.env.RESTART_MAX_DELAY_MS = '5000'; // 5 seconds
process.env.RESTART_BACKOFF_MULTIPLIER = '2';

console.log('Testing Restart Manager...');
console.log('Environment:', {
  NODE_ENV: process.env.NODE_ENV,
  AUTO_RESTART_ENABLED: process.env.AUTO_RESTART_ENABLED,
  MAX_RESTARTS: process.env.MAX_RESTARTS,
  RESTART_WINDOW_MS: process.env.RESTART_WINDOW_MS,
});

async function testRestartManager() {
  try {
    // Import the restart manager
    const { restartManager } = require('./dist/shared/lib/restart-manager');
    
    console.log('\n1. Testing restart manager initialization...');
    const stats = restartManager.getStats();
    console.log('Initial stats:', stats);
    
    console.log('\n2. Testing restart attempt (should succeed)...');
    const result1 = await restartManager.attemptRestart('Test restart 1');
    console.log('Restart attempt 1 result:', result1);
    
    // Wait a moment
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    console.log('\n3. Testing second restart attempt...');
    const result2 = await restartManager.attemptRestart('Test restart 2');
    console.log('Restart attempt 2 result:', result2);
    
    // Wait a moment
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    console.log('\n4. Testing third restart attempt...');
    const result3 = await restartManager.attemptRestart('Test restart 3');
    console.log('Restart attempt 3 result:', result3);
    
    // Wait a moment
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    console.log('\n5. Testing fourth restart attempt (should fail - limit reached)...');
    const result4 = await restartManager.attemptRestart('Test restart 4 - should fail');
    console.log('Restart attempt 4 result:', result4);
    
    console.log('\n6. Final stats:');
    const finalStats = restartManager.getStats();
    console.log('Final stats:', finalStats);
    
    console.log('\n7. Testing reset functionality...');
    restartManager.resetRestartCount();
    const resetStats = restartManager.getStats();
    console.log('Stats after reset:', resetStats);
    
    console.log('\n✅ Restart manager test completed successfully!');
    
  } catch (error) {
    console.error('❌ Test failed:', error);
    process.exit(1);
  }
}

// Mock the process restart to prevent actual restart during testing
const originalExit = process.exit;
process.exit = function(code) {
  console.log(`🔄 Process would exit with code: ${code} (mocked for testing)`);
  // Don't actually exit during testing
  return originalExit;
};

// Run the test
testRestartManager().then(() => {
  console.log('\nTest completed. Process will continue running...');
  // Restore original exit function
  process.exit = originalExit;
}).catch(error => {
  console.error('Test error:', error);
  process.exit = originalExit;
  process.exit(1);
});
