# Queue Management Implementation

## Overview

This document describes the comprehensive queue management system implemented for sock messages in the liteserver application. The system provides queue size limits, automatic cleanup, monitoring, and health metrics.

## Features Implemented

### 1. Queue Size Management
- **Configurable maximum queue size** per competition
- **FIFO eviction** when queue size exceeds limits
- **Warning thresholds** to alert before reaching limits
- **Real-time queue size monitoring**

### 2. Time-Based Message Expiry
- **Configurable message age limits** (default: 1 hour)
- **Automatic cleanup** of expired messages
- **Scheduled cleanup service** (default: every 5 minutes)

### 3. Queue Monitoring & Metrics
- **Global queue metrics** (total messages, peak size, etc.)
- **Per-competition queue statistics**
- **Queue health monitoring** via admin API
- **Cleanup service status** tracking

### 4. Admin API Endpoints
- `GET /api/admin/queue/metrics` - Global queue metrics
- `GET /api/admin/queue/stats/:competitionId` - Competition-specific stats
- `GET /api/admin/queue/service-status` - Cleanup service status
- `POST /api/admin/queue/cleanup` - Manual cleanup trigger

## Configuration

### Environment Variables

#### Production (.env.production)
```bash
# Message Queue Configuration
MESSAGE_QUEUE_MAX_SIZE=1000              # Maximum messages per competition
MESSAGE_QUEUE_MAX_AGE_MS=3600000         # 1 hour message expiry
MESSAGE_QUEUE_WARNING_THRESHOLD=800      # Warning at 80% capacity
QUEUE_CLEANUP_INTERVAL_MS=300000         # 5 minute cleanup interval
```

#### Development (ecosystem.config.js)
```javascript
// Smaller limits for development
MESSAGE_QUEUE_MAX_SIZE: '500'
MESSAGE_QUEUE_MAX_AGE_MS: '1800000'      // 30 minutes
MESSAGE_QUEUE_WARNING_THRESHOLD: '400'
QUEUE_CLEANUP_INTERVAL_MS: '180000'      // 3 minutes
```

## Implementation Details

### Core Components

#### 1. Enhanced CompetitionCache (`src/features/competitions/services/competition-cache.ts`)
- **TimestampedMessage interface** - Messages with queue timestamps
- **QueueConfig interface** - Configurable queue parameters
- **Queue metrics tracking** - Performance and health metrics
- **Automatic cleanup methods** - Expired message removal and size enforcement

#### 2. QueueCleanupService (`src/features/competitions/services/queue-cleanup-service.ts`)
- **Scheduled cleanup** - Periodic removal of expired messages
- **Service status tracking** - Monitor cleanup service health
- **Configurable intervals** - Adjustable cleanup frequency
- **Graceful shutdown** - Proper cleanup on application exit

#### 3. Admin API Controllers (`src/features/admin/controllers/index.ts`)
- **Queue metrics endpoint** - Global queue health data
- **Queue statistics endpoint** - Per-competition queue data
- **Manual cleanup endpoint** - On-demand queue cleanup
- **Service status endpoint** - Cleanup service monitoring

### Key Methods

#### Queue Management
```typescript
// Store message with size and age limits
storeMessage(competitionId: number, message: ExternalSocketMessage): void

// Get queue statistics for monitoring
getQueueStats(competitionId: number): QueueStats

// Get global queue metrics
getQueueMetrics(): GlobalQueueMetrics

// Perform cleanup of expired messages
performGlobalCleanup(): CleanupResult
```

#### Size Enforcement
```typescript
// Remove expired messages based on age
private cleanExpiredMessages(queue: TimestampedMessage[]): number

// Enforce FIFO eviction when size limit exceeded
private enforceQueueSizeLimit(queue: TimestampedMessage[], competitionId: number): number

// Check and warn when approaching size threshold
private checkQueueWarningThreshold(queue: TimestampedMessage[], competitionId: number): void
```

## Queue Behavior

### Message Storage Flow
1. **Filter interested messages** - Only store relevant message types
2. **Clean expired messages** - Remove old messages before adding new ones
3. **Add timestamped message** - Store with queue timestamp
4. **Enforce size limits** - Remove oldest messages if over limit (FIFO)
5. **Check warning threshold** - Log warnings if approaching capacity
6. **Update metrics** - Track queue statistics and performance

### Cleanup Process
1. **Scheduled cleanup** - Runs every 5 minutes (configurable)
2. **Age-based expiry** - Remove messages older than 1 hour (configurable)
3. **Empty queue removal** - Delete empty queues to free memory
4. **Metrics logging** - Periodic queue health reporting

## Monitoring

### Queue Metrics Available
- **Total messages processed** - Lifetime message count
- **Total messages evicted** - Messages removed due to size limits
- **Total messages expired** - Messages removed due to age
- **Peak queue size** - Highest queue size reached
- **Active queues** - Number of competitions with queued messages
- **Total queued messages** - Current total across all queues

### Warning Conditions
- **Queue size warnings** - When queue reaches 80% of max size (configurable)
- **Size limit exceeded** - When FIFO eviction occurs
- **Cleanup failures** - When cleanup operations fail

## Testing

### Test Coverage
- **Queue size limits** - Verify size enforcement works
- **Message filtering** - Ensure only interested messages are stored
- **Queue cleanup** - Test expired message removal
- **Statistics accuracy** - Verify metrics are correct
- **Message retrieval** - Ensure internal timestamps are removed

### Running Tests
```bash
npm test src/features/competitions/services/__tests__/queue-management.test.ts
```

## Performance Considerations

### Memory Management
- **Automatic cleanup** prevents unbounded memory growth
- **FIFO eviction** maintains recent message history
- **Empty queue removal** frees unused memory
- **Configurable limits** allow tuning for available resources

### CPU Impact
- **Efficient cleanup** - O(n) operations for message filtering
- **Scheduled processing** - Cleanup runs periodically, not on every message
- **Minimal overhead** - Queue operations are lightweight

## Deployment

### Application Startup
The queue management system is automatically started with the application:
1. **Queue cleanup service** starts with configurable interval
2. **Existing queues** are preserved across restarts
3. **Graceful shutdown** stops cleanup service properly

### Monitoring in Production
- **Admin dashboard** - View queue health via `/admin-dashboard`
- **API endpoints** - Programmatic access to queue metrics
- **Log monitoring** - Queue warnings and errors in application logs

## Future Enhancements

### Potential Improvements
- **Queue persistence** - Store queues to disk for restart recovery
- **Per-message priority** - Priority-based message ordering
- **Queue partitioning** - Separate queues by message type
- **Advanced metrics** - Message processing rates, latency tracking
- **Alert integration** - External alerting for queue health issues
