#!/usr/bin/env node

// Test script to verify TypeScript compilation works
// Run with: node test-typescript-compilation.js

const { spawn } = require('child_process');
const path = require('path');

console.log('🔍 Testing TypeScript compilation...');

// Run TypeScript compilation
const tscProcess = spawn('npx', ['tsc', '--noEmit'], {
  cwd: process.cwd(),
  stdio: 'pipe'
});

let stdout = '';
let stderr = '';

tscProcess.stdout.on('data', (data) => {
  stdout += data.toString();
});

tscProcess.stderr.on('data', (data) => {
  stderr += data.toString();
});

tscProcess.on('close', (code) => {
  if (code === 0) {
    console.log('✅ TypeScript compilation successful!');
    console.log('No compilation errors found.');
    
    // Now test the actual build process
    console.log('\n🔨 Testing full build process...');
    
    const buildProcess = spawn('npm', ['run', 'build'], {
      cwd: process.cwd(),
      stdio: 'pipe'
    });
    
    let buildStdout = '';
    let buildStderr = '';
    
    buildProcess.stdout.on('data', (data) => {
      buildStdout += data.toString();
    });
    
    buildProcess.stderr.on('data', (data) => {
      buildStderr += data.toString();
    });
    
    buildProcess.on('close', (buildCode) => {
      if (buildCode === 0) {
        console.log('✅ Build process completed successfully!');
        console.log('Application is ready for deployment.');
        
        // Test if the built files exist
        const fs = require('fs');
        const distPath = path.join(process.cwd(), 'dist');
        const indexPath = path.join(distPath, 'index.js');
        
        if (fs.existsSync(indexPath)) {
          console.log('✅ Built files found in dist/ directory');
          console.log('📁 Main entry point: dist/index.js');
          
          // List some key built files
          try {
            const files = fs.readdirSync(distPath, { recursive: true });
            console.log('\n📋 Built files:');
            files.slice(0, 10).forEach(file => {
              console.log(`   - ${file}`);
            });
            if (files.length > 10) {
              console.log(`   ... and ${files.length - 10} more files`);
            }
          } catch (error) {
            console.log('Could not list built files:', error.message);
          }
          
        } else {
          console.log('⚠️  Warning: dist/index.js not found after build');
        }
        
      } else {
        console.log('❌ Build process failed!');
        console.log('Build stdout:', buildStdout);
        console.log('Build stderr:', buildStderr);
        process.exit(1);
      }
    });
    
  } else {
    console.log('❌ TypeScript compilation failed!');
    console.log('Exit code:', code);
    
    if (stdout) {
      console.log('\nStdout:');
      console.log(stdout);
    }
    
    if (stderr) {
      console.log('\nStderr:');
      console.log(stderr);
    }
    
    // Try to extract specific error information
    const output = stdout + stderr;
    if (output.includes('TS2554')) {
      console.log('\n💡 Tip: TS2554 errors are usually about incorrect number of arguments.');
      console.log('   Check logger calls and function signatures.');
    }
    
    if (output.includes('logger')) {
      console.log('\n💡 Tip: Logger-related errors detected.');
      console.log('   Make sure logger calls use: logger.method(message, metadata)');
      console.log('   Not: logger.method(arg1, arg2, arg3, arg4)');
    }
    
    process.exit(1);
  }
});

// Handle process errors
tscProcess.on('error', (error) => {
  console.error('❌ Failed to start TypeScript compiler:', error.message);
  console.log('\n💡 Make sure TypeScript is installed:');
  console.log('   npm install -g typescript');
  console.log('   or use: npx tsc --version');
  process.exit(1);
});
