module.exports = {
  onConnect: function(socket, context, callback) {
    console.log('Socket connected with ID:', socket.id);
    
    // Just log connection and disconnection events
    socket.on('disconnect', (reason) => {
      console.log('Socket disconnected:', socket.id, 'Reason:', reason);
    });
    
    socket.on('connect_error', (error) => {
      console.log('Socket connect error:', socket.id, 'Error:', error);
    });
    
    callback();
  }
};