const { spawn } = require('child_process');
const fs = require('fs');

// Test scenarios
const scenarios = [
  // {
  //   name: 'low_load',
  //   config: {
  //     NUM_CLIENTS: 5,
  //     TEST_DURATION_MS: 10000,
  //     RAMP_UP_MS: 1000,
  //     MESSAGE_INTERVAL_MS: 1000,
  //     LOG_FILE: 'socket-test-low-load.log',
  //   },
  // },
  // {
  //   name: 'medium_load',
  //   config: {
  //     NUM_CLIENTS: 100,
  //     TEST_DURATION_MS: 20000,
  //     RAMP_UP_MS: 5000,
  //     MESSAGE_INTERVAL_MS: 2000,
  //     LOG_FILE: 'socket-test-medium-load.log',
  //   },
  // },
  {
    name: 'high_load',
    config: {
      NUM_CLIENTS: 750,
      TEST_DURATION_MS: 120000,
      RAMP_UP_MS: 50000,
      MESSAGE_INTERVAL_MS: 2000,
      LOG_FILE: 'socket-test-high-load.log',
    },
  },
];
// const scenarios = [
//   {
//     name: 'low_load',
//     config: {
//       NUM_CLIENTS: 500,
//       TEST_DURATION_MS: 30000,
//       RAMP_UP_MS: 5000,
//       MESSAGE_INTERVAL_MS: 5000,
//       LOG_FILE: 'socket-test-low-load.log',
//     },
//   },
//   {
//     name: 'medium_load',
//     config: {
//       NUM_CLIENTS: 1000,
//       TEST_DURATION_MS: 60000,
//       RAMP_UP_MS: 10000,
//       MESSAGE_INTERVAL_MS: 3000,
//       LOG_FILE: 'socket-test-medium-load.log',
//     },
//   },
//   {
//     name: 'high_load',
//     config: {
//       NUM_CLIENTS: 1500,
//       TEST_DURATION_MS: 120000,
//       RAMP_UP_MS: 20000,
//       MESSAGE_INTERVAL_MS: 2000,
//       LOG_FILE: 'socket-test-high-load.log',
//     },
//   },
// ];

// Function to run a test scenario
function runScenario(scenario) {
  return new Promise((resolve, reject) => {
    console.log(`\n=== Running scenario: ${scenario.name} ===`);
    console.log(`Clients: ${scenario.config.NUM_CLIENTS}`);
    console.log(`Duration: ${scenario.config.TEST_DURATION_MS / 1000} seconds`);
    console.log(`Ramp-up: ${scenario.config.RAMP_UP_MS / 1000} seconds`);
    console.log(`Message interval: ${scenario.config.MESSAGE_INTERVAL_MS / 1000} seconds\n`);

    const env = { ...process.env, ...scenario.config };
    const child = spawn('node', ['load-tests/custom-socket-client.js'], { env });

    child.stdout.on('data', data => {
      process.stdout.write(data.toString());
    });

    child.stderr.on('data', data => {
      process.stderr.write(data.toString());
    });

    child.on('close', code => {
      if (code === 0) {
        console.log(`\n=== Scenario ${scenario.name} completed successfully ===\n`);
        resolve();
      } else {
        console.error(`\n=== Scenario ${scenario.name} failed with code ${code} ===\n`);
        reject(new Error(`Scenario ${scenario.name} failed with code ${code}`));
      }
    });
  });
}

// Run scenarios sequentially
async function runTests() {
  console.log('Starting Socket.IO load tests');

  // Create results directory if it doesn't exist
  if (!fs.existsSync('load-test-results')) {
    fs.mkdirSync('load-test-results');
  }

  for (const scenario of scenarios) {
    try {
      await runScenario(scenario);

      // Move result files to results directory
      if (fs.existsSync(scenario.config.LOG_FILE)) {
        fs.renameSync(
          scenario.config.LOG_FILE,
          `load-test-results/${scenario.name}-${new Date().toISOString().replace(/:/g, '-')}.log`
        );
      }

      if (fs.existsSync('socket-test-metrics.json')) {
        fs.renameSync(
          'socket-test-metrics.json',
          `load-test-results/${scenario.name}-metrics-${new Date().toISOString().replace(/:/g, '-')}.json`
        );
      }

      // Wait a bit between scenarios
      await new Promise(resolve => setTimeout(resolve, 5000));
    } catch (error) {
      console.error(`Error running scenario ${scenario.name}:`, error);
    }
  }

  console.log('All tests completed');
}

runTests().catch(console.error);
