module.exports = {
  onConnect: function (socket, context, callback) {
    console.log('Socket connected with ID:', socket.id);

    // Keep the connection alive
    const keepAlive = setInterval(() => {
      if (socket.connected) {
        console.log('Keeping connection alive for socket:', socket.id);
      } else {
        clearInterval(keepAlive);
      }
    }, 1000);

    // Listen for disconnect events
    socket.on('disconnect', reason => {
      console.log('Socket disconnected:', socket.id, 'Reason:', reason);
      clearInterval(keepAlive);
    });

    // Listen for response events
    socket.on('response', data => {
      console.log('Received response:', data);
    });

    callback();
  },
};
