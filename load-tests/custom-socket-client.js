const { io } = require('socket.io-client');
const fs = require('fs');

// wss://lite.entry4sports.co.uk/socket.io/?EIO=4&transport=websocket&sid=6I8WzO9VTshdSGZzAAAN
// Configuration - can be adjusted for different test scenarios
// const SERVER_URL = process.env.SERVER_URL || 'http://localhost:3001';
const SERVER_URL = 'https://live.entry4sports.co.uk';
const NUM_CLIENTS = parseInt(process.env.NUM_CLIENTS || '10', 10);
const TEST_DURATION_MS = parseInt(process.env.TEST_DURATION_MS || '30000', 10);
const RAMP_UP_MS = parseInt(process.env.RAMP_UP_MS || '5000', 10);
const MESSAGE_INTERVAL_MS = parseInt(process.env.MESSAGE_INTERVAL_MS || '5000', 10);
const LOG_FILE = process.env.LOG_FILE || 'socket-test-results.log';

// Metrics
const metrics = {
  connectSuccess: 0,
  connectFail: 0,
  messagesSent: 0,
  messagesReceived: 0,
  errors: 0,
  disconnects: 0,
  responseTimeMs: [],
};

// Clear log file
fs.writeFileSync(LOG_FILE, '');

// Log function
function log(message) {
  const timestamp = new Date().toISOString();
  const logMessage = `${timestamp} - ${message}\n`;
  console.log(message);
  fs.appendFileSync(LOG_FILE, logMessage);
}

log(`Starting Socket.IO test with ${NUM_CLIENTS} clients`);
log(`Server URL: ${SERVER_URL}`);
log(`Test duration: ${TEST_DURATION_MS / 1000} seconds`);
log(`Ramp-up period: ${RAMP_UP_MS / 1000} seconds`);
log(`Message interval: ${MESSAGE_INTERVAL_MS / 1000} seconds`);

// Create clients with ramp-up
const clients = [];
const clientsPerBatch = Math.max(1, Math.floor(NUM_CLIENTS / (RAMP_UP_MS / 1000)));
const batchIntervalMs = Math.max(100, RAMP_UP_MS / Math.ceil(NUM_CLIENTS / clientsPerBatch));

log(`Creating ${clientsPerBatch} clients every ${batchIntervalMs}ms`);

let clientsCreated = 0;
const createBatch = () => {
  const batchSize = Math.min(clientsPerBatch, NUM_CLIENTS - clientsCreated);

  for (let i = 0; i < batchSize; i++) {
    const clientIndex = clientsCreated + i;
    // path: '/custom/socket.io',
    const socket = io(SERVER_URL, {
      transports: ['websocket', 'polling'],
      reconnection: true,
      reconnectionAttempts: 5,
      reconnectionDelay: 1000,
      timeout: 20000,
    });

    const clientId = `client-${clientIndex + 1}`;

    socket.on('connect', () => {
      metrics.connectSuccess++;
      log(`${clientId} connected with socket ID: ${socket.id}`);

      // Set up periodic message sending
      const messageInterval = setInterval(() => {
        if (socket.connected) {
          const timestamp = Date.now();
          log(`${clientId} sending message`);
          socket.emit('message', {
            text: `Hello from ${clientId}`,
            timestamp: timestamp,
            messageId: `${clientId}-${timestamp}`,
          });
          metrics.messagesSent++;
        }
      }, MESSAGE_INTERVAL_MS);

      // Store the interval so we can clear it later
      socket.messageInterval = messageInterval;
    });

    socket.on('response', data => {
      log(`${clientId} received response: ${JSON.stringify(data)}`);
      metrics.messagesReceived++;

      // Calculate response time if the response includes the original timestamp
      if (data.received && data.received.timestamp) {
        const responseTime = Date.now() - data.received.timestamp;
        metrics.responseTimeMs.push(responseTime);
        log(`${clientId} response time: ${responseTime}ms`);
      }
    });

    socket.on('disconnect', reason => {
      log(`${clientId} disconnected. Reason: ${reason}`);
      metrics.disconnects++;
      if (socket.messageInterval) {
        clearInterval(socket.messageInterval);
      }
    });

    socket.on('connect_error', error => {
      log(`${clientId} connection error: ${error.message}`);
      metrics.connectFail++;
      metrics.errors++;
    });

    socket.on('error', error => {
      log(`${clientId} error: ${error.message}`);
      metrics.errors++;
    });

    clients.push({ id: clientId, socket });
  }

  clientsCreated += batchSize;
  log(`Created ${batchSize} clients, total: ${clientsCreated}/${NUM_CLIENTS}`);

  if (clientsCreated < NUM_CLIENTS) {
    setTimeout(createBatch, batchIntervalMs);
  }
};

// Start creating clients
createBatch();

// Run test for specified duration
log(`Test running for ${TEST_DURATION_MS / 1000} seconds...`);

// Print metrics periodically
const metricsInterval = setInterval(() => {
  const avgResponseTime =
    metrics.responseTimeMs.length > 0
      ? metrics.responseTimeMs.reduce((sum, time) => sum + time, 0) / metrics.responseTimeMs.length
      : 0;

  log(`
--- METRICS UPDATE ---
Connections: Success=${metrics.connectSuccess}, Failed=${metrics.connectFail}
Messages: Sent=${metrics.messagesSent}, Received=${metrics.messagesReceived}
Errors: ${metrics.errors}
Disconnects: ${metrics.disconnects}
Avg Response Time: ${avgResponseTime.toFixed(2)}ms
Active Clients: ${clients.filter(c => c.socket.connected).length}/${clients.length}
---------------------
  `);
}, 5000);

// End the test after the specified duration
setTimeout(() => {
  clearInterval(metricsInterval);

  // Calculate final metrics
  const avgResponseTime =
    metrics.responseTimeMs.length > 0
      ? metrics.responseTimeMs.reduce((sum, time) => sum + time, 0) / metrics.responseTimeMs.length
      : 0;

  // Sort response times and calculate percentiles
  const sortedResponseTimes = [...metrics.responseTimeMs].sort((a, b) => a - b);
  const p50 = sortedResponseTimes[Math.floor(sortedResponseTimes.length * 0.5)] || 0;
  const p90 = sortedResponseTimes[Math.floor(sortedResponseTimes.length * 0.9)] || 0;
  const p95 = sortedResponseTimes[Math.floor(sortedResponseTimes.length * 0.95)] || 0;
  const p99 = sortedResponseTimes[Math.floor(sortedResponseTimes.length * 0.99)] || 0;

  log(`
=== TEST COMPLETE ===
Test Duration: ${TEST_DURATION_MS / 1000} seconds
Clients: ${NUM_CLIENTS}
Connections: Success=${metrics.connectSuccess}, Failed=${metrics.connectFail}
Messages: Sent=${metrics.messagesSent}, Received=${metrics.messagesReceived}
Errors: ${metrics.errors}
Disconnects: ${metrics.disconnects}
Response Times:
  Average: ${avgResponseTime.toFixed(2)}ms
  P50: ${p50}ms
  P90: ${p90}ms
  P95: ${p95}ms
  P99: ${p99}ms
===================
  `);

  log('Test complete, disconnecting clients');

  // Disconnect all clients
  clients.forEach(client => {
    if (client.socket.connected) {
      log(`Disconnecting ${client.id}`);
      if (client.socket.messageInterval) {
        clearInterval(client.socket.messageInterval);
      }
      client.socket.disconnect();
    }
  });

  log('Test finished');

  // Save metrics to a JSON file for later analysis
  fs.writeFileSync(
    'socket-test-metrics.json',
    JSON.stringify(
      {
        config: {
          serverUrl: SERVER_URL,
          numClients: NUM_CLIENTS,
          testDurationMs: TEST_DURATION_MS,
          rampUpMs: RAMP_UP_MS,
          messageIntervalMs: MESSAGE_INTERVAL_MS,
        },
        metrics: {
          ...metrics,
          responseTimes: {
            average: avgResponseTime,
            p50,
            p90,
            p95,
            p99,
          },
        },
      },
      null,
      2
    )
  );

  process.exit(0);
}, TEST_DURATION_MS);
