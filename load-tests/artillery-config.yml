config:
  target: "http://localhost:3000"
  phases:
    - duration: 10
      arrivalRate: 1
      name: "Steady connection phase"
  defaults:
    headers:
      User-Agent: "Artillery Load Test"
  socketio:
    path: "/custom/socket.io"
    transports: ["websocket"]
    namespace: "/"
    version: "4"  # Explicitly set to Socket.IO v4
    reconnection: true
    reconnectionAttempts: 5
    reconnectionDelay: 1000
    timeout: 20000
  processor: "./socket-functions.js"
  verbose: true

scenarios:
  - name: "Socket.IO connections"
    engine: "socketio"
    flow:
      - log: "Connecting to Socket.IO server"
      - connect:
          function: "onConnect"
      - think: 5
      - log: "Subscribing to competition"
      - emit:
          channel: "message"
          data: >
            {"key": "test-{{ $randomString(8) }}", "comp": {"id": 617}, "action": "subscribe", "deviceKey": "{{ $randomNumber(1000, 9999) }}", "securityKey": "", "domain": "localhost", "payload": {"compId": 617}}
      - think: 15
      - log: "Sending ping"
      - emit:
          channel: "message"
          data: >
            {"key": "ping-{{ $randomString(8) }}", "comp": {"id": 617}, "action": "ping", "deviceKey": "{{ $randomNumber(1000, 9999) }}", "securityKey": "", "domain": "localhost", "payload": {"ping": true}}
      - think: 15
      - log: "Test complete, waiting before disconnect"
      - think: 10
