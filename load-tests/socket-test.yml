config:
  target: "http://localhost:3001"
  phases:
    - duration: 10
      arrivalRate: 1
  socketio:
    path: "/custom/socket.io"
    transports: ["websocket", "polling"]  # Try both transports
    query: "EIO=4&transport=websocket"    # Explicitly set protocol version
    reconnection: true
  processor: "./socket-functions.js"

scenarios:
  - name: "Socket.IO Test"
    engine: "socketio"
    flow:
      - connect:
          function: "onConnect"
      - think: 2
      - emit:
          channel: "message"
          data: { "text": "Hello from Artillery" }
      - think: 5
