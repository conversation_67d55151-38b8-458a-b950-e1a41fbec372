APP_PORT=8765
DB_PORT=3306
DB_USER=e4sadmin
DB_PASSWORD=E4SEntrySystem2025$
LOG_LEVEL=info

# Disable console logging in production to prevent EPIPE errors
ENABLE_CONSOLE_LOGGING=false

# Auto-restart configuration
AUTO_RESTART_ENABLED=true
MAX_RESTARTS=5
RESTART_WINDOW_MS=300000
RESTART_INITIAL_DELAY_MS=2000
RESTART_MAX_DELAY_MS=60000
RESTART_BACKOFF_MULTIPLIER=2

# Health monitoring configuration
HEALTH_MONITOR_ENABLED=true
HEALTH_CHECK_INTERVAL_MS=30000
HEALTH_UNHEALTHY_THRESHOLD=3
HEALTH_MEMORY_THRESHOLD_MB=400

#LIVE
DB_NAME=E4S
DB_HOST=************
#/LIVE

# WebSocket Configuration
EXTERNAL_WS_URL=wss://m9f8tsofz8.execute-api.eu-west-2.amazonaws.com/Prod

# Cache Configuration
COMPETITION_REFRESH_INTERVAL_MS=10000
COMPETITION_CACHE_TTL=10000

# Message Queue Configuration
MESSAGE_QUEUE_MAX_SIZE=1000
MESSAGE_QUEUE_MAX_AGE_MS=3600000
MESSAGE_QUEUE_WARNING_THRESHOLD=800
QUEUE_CLEANUP_INTERVAL_MS=300000

# Client config
CLIENT_COMPETITION_REFRESH_INTERVAL_MS=60000

# Only for load testing !!!!!!!!!!!!!!!!!!!!!!!!!!!!!
RATE_LIMIT_ENABLED=true

# SQL
DB_CONNECTION_LIMIT=20

# Add these environment variables
GOOGLE_CLIENT_ID=541640013880-ejhd6j3m5c7913e384pb6i0u4kporkre.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=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
# This is key we (E4S) generated, any old random string.
JWT_SECRET=c4f2dc964b5be207aa5f01182b9ce625d72baf726eabee26b59dc34a2f3d46092d2b708c0e4ff75f9853af0669072ac02bd988236cffbab7332b92c48b611ed4
SESSION_SECRET=21b4b00a66bf99ec8d0d4be05f16fd642ae961f29424bcb799ffe9c73b341b6d
# Default client redirect URL
CLIENT_REDIRECT_URL=https://live.entry4sports.co.uk/liteclient/index.html
# Google OAuth callback URL for production
GOOGLE_CALLBACK_URL=https://live.entry4sports.co.uk/api/auth/google/callback
# Comma-separated list of admin email addresses
ADMIN_EMAILS=<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>


# Comma-separated list of allowed domains for websocket messages (production only allows main domain)
ALLOWED_DOMAINS=entry4sports.co.uk

# Database Startup Retry Configuration
DB_STARTUP_RETRIES=5
DB_STARTUP_RETRY_DELAY=2000


# WordPress Authentication
WP_USERNAME=your_wp_username
WP_PASSWORD=your_wp_password
WP_LOGIN_URL=https://entry4sports.co.uk/wp-login.php
WP_API_BASE_URL=https://entry4sports.co.uk/wp-json/wp/v2

# WordPress connection behavior
WP_CONNECTION_ENABLED=true
REQUIRE_WP_ON_STARTUP=false
# 1hr = 3600000
WP_RECONNECT_INTERVAL_MS=3600000
