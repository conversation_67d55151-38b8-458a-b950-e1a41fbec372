[Unit]
Description=LiteServer Node.js Application
Documentation=https://github.com/your-repo/liteserver
After=network.target mysql.service
Wants=mysql.service

[Service]
Type=simple
User=www-data
Group=www-data
WorkingDirectory=/var/www/liteserver
Environment=NODE_ENV=production
Environment=PATH=/usr/bin:/usr/local/bin
ExecStart=/usr/bin/node dist/index.js
ExecReload=/bin/kill -USR1 $MAINPID

# Restart configuration
Restart=always
RestartSec=10
StartLimitInterval=60s
StartLimitBurst=3

# Resource limits
LimitNOFILE=65536
LimitNPROC=4096

# Security settings
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=/var/www/liteserver/logs

# Logging
StandardOutput=journal
StandardError=journal
SyslogIdentifier=liteserver

# Kill settings
KillMode=mixed
KillSignal=SIGTERM
TimeoutStopSec=30

[Install]
WantedBy=multi-user.target
