const { dbConnect } = require('../dist/shared/lib/db');
const { restartManager } = require('../dist/shared/lib/restart-manager');

describe('Database Connection Retry', () => {
  beforeEach(() => {
    // Reset restart manager state
    restartManager.resetRestartCount();
  });

  test('should retry database connection on timeout', async () => {
    // Mock a database connection that fails twice then succeeds
    let attemptCount = 0;
    const originalTestConnection = dbConnect.testConnection;
    
    dbConnect.testConnection = jest.fn().mockImplementation(async () => {
      attemptCount++;
      if (attemptCount <= 2) {
        const error = new Error('Connection timeout');
        error.code = 'ETIMEDOUT';
        throw error;
      }
      return true;
    });

    // Test with 3 retries, should succeed on 3rd attempt
    const result = await dbConnect.testConnectionWithRetry(3, 100);
    
    expect(result).toBe(true);
    expect(attemptCount).toBe(3);
    expect(dbConnect.testConnection).toHaveBeenCalledTimes(3);

    // Restore original function
    dbConnect.testConnection = originalTestConnection;
  }, 10000);

  test('should fail after max retries exceeded', async () => {
    // Mock a database connection that always fails
    const originalTestConnection = dbConnect.testConnection;
    
    dbConnect.testConnection = jest.fn().mockImplementation(async () => {
      const error = new Error('Connection timeout');
      error.code = 'ETIMEDOUT';
      throw error;
    });

    // Test with 2 retries, should fail
    await expect(dbConnect.testConnectionWithRetry(2, 100)).rejects.toThrow('Connection timeout');
    expect(dbConnect.testConnection).toHaveBeenCalledTimes(2);

    // Restore original function
    dbConnect.testConnection = originalTestConnection;
  }, 10000);

  test('should not retry non-timeout errors', async () => {
    // Mock a database connection that fails with auth error
    const originalTestConnection = dbConnect.testConnection;
    
    dbConnect.testConnection = jest.fn().mockImplementation(async () => {
      const error = new Error('Access denied');
      error.code = 'ER_ACCESS_DENIED_ERROR';
      throw error;
    });

    // Should fail immediately without retries
    await expect(dbConnect.testConnectionWithRetry(3, 100)).rejects.toThrow('Access denied');
    expect(dbConnect.testConnection).toHaveBeenCalledTimes(1);

    // Restore original function
    dbConnect.testConnection = originalTestConnection;
  });

  test('should use exponential backoff for retries', async () => {
    const startTime = Date.now();
    let attemptCount = 0;
    const originalTestConnection = dbConnect.testConnection;
    
    dbConnect.testConnection = jest.fn().mockImplementation(async () => {
      attemptCount++;
      if (attemptCount <= 2) {
        const error = new Error('Connection timeout');
        error.code = 'ETIMEDOUT';
        throw error;
      }
      return true;
    });

    // Test with retries and measure time
    await dbConnect.testConnectionWithRetry(3, 1000);
    const endTime = Date.now();
    const totalTime = endTime - startTime;

    // Should take at least the sum of delays: 1000ms + 1500ms = 2500ms
    expect(totalTime).toBeGreaterThan(2400); // Allow some tolerance
    expect(attemptCount).toBe(3);

    // Restore original function
    dbConnect.testConnection = originalTestConnection;
  }, 15000);
});

describe('Restart Manager Startup Handling', () => {
  test('should handle startup failure gracefully', async () => {
    // Mock process.exit to prevent actual exit during test
    const originalExit = process.exit;
    const mockExit = jest.fn();
    process.exit = mockExit;

    // Mock restart manager to simulate restart failure
    const originalAttemptRestart = restartManager.attemptRestart;
    restartManager.attemptRestart = jest.fn().mockResolvedValue(false);

    // Test startup failure handling
    await restartManager.handleStartupFailure('Test failure');

    expect(restartManager.attemptRestart).toHaveBeenCalledWith('Startup failure: Test failure');
    expect(mockExit).toHaveBeenCalledWith(1);

    // Restore original functions
    process.exit = originalExit;
    restartManager.attemptRestart = originalAttemptRestart;
  });
});
