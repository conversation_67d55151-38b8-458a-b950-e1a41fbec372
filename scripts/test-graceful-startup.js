#!/usr/bin/env node

/**
 * Test script to demonstrate graceful degradation behavior
 * 
 * This script will:
 * 1. Start the server with an invalid database configuration
 * 2. Show that the server still starts and accepts connections
 * 3. Test the health endpoint to show degraded status
 * 4. Demonstrate API responses when database is unavailable
 */

const http = require('http');
const { spawn } = require('child_process');
const path = require('path');

// Test configuration with invalid database to simulate unavailability
const testEnv = {
  ...process.env,
  REQUIRE_DB_ON_STARTUP: 'false',
  DB_HOST: 'invalid-host-that-does-not-exist.local',
  DB_PORT: '9999',
  APP_PORT: '3001', // Use different port for testing
  LOG_LEVEL: 'info',
  DB_STARTUP_RETRIES: '2', // Reduce retries for faster testing
  DB_STARTUP_RETRY_DELAY: '1000',
  DB_RECONNECT_INTERVAL_MS: '10000' // Faster reconnection for demo
};

console.log('=== Graceful Degradation Test ===\n');
console.log('Starting server with invalid database configuration...');
console.log('Expected behavior: Server should start despite database being unavailable\n');

// Start the server
const serverProcess = spawn('node', ['dist/index.js'], {
  env: testEnv,
  cwd: path.join(__dirname, '..'),
  stdio: ['pipe', 'pipe', 'pipe']
});

let serverOutput = '';
let serverStarted = false;

serverProcess.stdout.on('data', (data) => {
  const output = data.toString();
  serverOutput += output;
  console.log('SERVER:', output.trim());
  
  if (output.includes('Server running on port 3001')) {
    serverStarted = true;
    console.log('\n✅ Server started successfully despite database being unavailable!\n');
    
    // Wait a moment for startup to complete, then run tests
    setTimeout(runTests, 3000);
  }
});

serverProcess.stderr.on('data', (data) => {
  console.log('SERVER ERROR:', data.toString().trim());
});

serverProcess.on('close', (code) => {
  console.log(`\nServer process exited with code ${code}`);
  if (code !== 0 && !serverStarted) {
    console.log('❌ Server failed to start - this indicates the graceful degradation is not working');
  }
});

// Test functions
async function runTests() {
  console.log('Running tests...\n');
  
  try {
    // Test 1: Health check
    console.log('1. Testing health endpoint...');
    const healthResponse = await makeRequest('/health');
    console.log('Health status:', healthResponse.status);
    console.log('Database status:', healthResponse.database?.status || 'unknown');
    console.log('✅ Health check working\n');
    
    // Test 2: Basic route
    console.log('2. Testing basic route...');
    const debugResponse = await makeRequest('/debug');
    console.log('Debug response:', debugResponse);
    console.log('✅ Basic routes working\n');
    
    // Test 3: API route (should show database unavailable)
    console.log('3. Testing API route...');
    try {
      const apiResponse = await makeRequest('/api/health');
      console.log('API response:', apiResponse);
    } catch (error) {
      console.log('API error (expected):', error.message);
    }
    console.log('✅ API routes handling database unavailability\n');
    
    console.log('=== Test Summary ===');
    console.log('✅ Server started without database');
    console.log('✅ Health endpoint shows degraded status');
    console.log('✅ Basic functionality remains available');
    console.log('✅ Database-dependent features gracefully degrade');
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
  } finally {
    // Clean up
    console.log('\nStopping test server...');
    serverProcess.kill('SIGTERM');
    
    setTimeout(() => {
      if (!serverProcess.killed) {
        serverProcess.kill('SIGKILL');
      }
      process.exit(0);
    }, 2000);
  }
}

// Helper function to make HTTP requests
function makeRequest(path) {
  return new Promise((resolve, reject) => {
    const options = {
      hostname: 'localhost',
      port: 3001,
      path: path,
      method: 'GET',
      timeout: 5000
    };
    
    const req = http.request(options, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        try {
          const parsed = JSON.parse(data);
          resolve(parsed);
        } catch (error) {
          resolve(data); // Return raw data if not JSON
        }
      });
    });
    
    req.on('error', (error) => {
      reject(error);
    });
    
    req.on('timeout', () => {
      req.destroy();
      reject(new Error('Request timeout'));
    });
    
    req.end();
  });
}

// Handle cleanup on exit
process.on('SIGINT', () => {
  console.log('\nReceived SIGINT, cleaning up...');
  if (serverProcess && !serverProcess.killed) {
    serverProcess.kill('SIGTERM');
  }
  process.exit(0);
});

// Timeout after 30 seconds
setTimeout(() => {
  console.log('\n❌ Test timeout - server may not have started properly');
  if (serverProcess && !serverProcess.killed) {
    serverProcess.kill('SIGTERM');
  }
  process.exit(1);
}, 30000);
