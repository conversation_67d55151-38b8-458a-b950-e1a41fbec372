#!/usr/bin/env node

/**
 * Test script for database connection retry functionality
 * 
 * Usage:
 *   node scripts/test-db-connection.js
 * 
 * This script will test the database connection with retry logic
 * and demonstrate how the system handles connection failures.
 */

require('dotenv').config();
const { dbConnect } = require('../dist/shared/lib/db');
const { logger } = require('../dist/shared/lib/logger');

async function testDatabaseConnection() {
  console.log('=== Database Connection Test ===\n');
  
  try {
    console.log('Testing basic database connection...');
    await dbConnect.testConnection();
    console.log('✅ Basic connection test passed\n');
  } catch (error) {
    console.log('❌ Basic connection test failed:', error.message);
    console.log('This is expected if database is not available\n');
  }

  try {
    console.log('Testing database connection with retry logic...');
    const maxRetries = 3;
    const retryDelay = 1000;
    
    console.log(`Configuration: ${maxRetries} retries, ${retryDelay}ms initial delay`);
    
    const startTime = Date.now();
    await dbConnect.testConnectionWithRetry(maxRetries, retryDelay);
    const endTime = Date.now();
    
    console.log(`✅ Connection with retry succeeded in ${endTime - startTime}ms\n`);
  } catch (error) {
    console.log('❌ Connection with retry failed:', error.message);
    console.log('This indicates the database is not available after retries\n');
  }

  try {
    console.log('Testing connection pool statistics...');
    // This will show current pool status
    const stats = {
      connectionLimit: process.env.DB_CONNECTION_LIMIT || '20',
      host: process.env.DB_HOST || 'localhost',
      port: process.env.DB_PORT || '3306',
      database: process.env.DB_NAME || 'myapp',
    };
    
    console.log('Pool configuration:', stats);
  } catch (error) {
    console.log('❌ Failed to get pool statistics:', error.message);
  }

  console.log('\n=== Test Complete ===');
  
  // Close the database connection pool
  try {
    await dbConnect.close();
    console.log('Database connection pool closed');
  } catch (error) {
    console.log('Warning: Failed to close database pool:', error.message);
  }
  
  process.exit(0);
}

// Handle errors
process.on('unhandledRejection', (error) => {
  console.error('Unhandled rejection:', error);
  process.exit(1);
});

process.on('uncaughtException', (error) => {
  console.error('Uncaught exception:', error);
  process.exit(1);
});

// Run the test
testDatabaseConnection().catch((error) => {
  console.error('Test failed:', error);
  process.exit(1);
});
