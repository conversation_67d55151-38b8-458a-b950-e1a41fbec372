#!/bin/bash

# LiteServer Deployment Script
# This script handles deployment with auto-restart capabilities

set -e  # Exit on any error

echo "🚀 Starting LiteServer deployment..."

# Configuration
APP_NAME="liteserver"
APP_DIR="/var/www/liteserver"
SERVICE_NAME="liteserver"
PM2_ECOSYSTEM="ecosystem.config.js"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if running as root or with sudo
if [[ $EUID -eq 0 ]]; then
    print_warning "Running as root. Consider using a non-root user for security."
fi

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    print_error "Node.js is not installed. Please install Node.js first."
    exit 1
fi

# Check if npm is installed
if ! command -v npm &> /dev/null; then
    print_error "npm is not installed. Please install npm first."
    exit 1
fi

print_status "Node.js version: $(node --version)"
print_status "npm version: $(npm --version)"

# Install dependencies
print_status "Installing dependencies..."
npm ci --production

# Build the application
print_status "Building application..."
npm run build

# Create logs directory if it doesn't exist
mkdir -p logs
chmod 755 logs

# Check deployment method preference
DEPLOYMENT_METHOD=""

# Check if PM2 is available
if command -v pm2 &> /dev/null; then
    DEPLOYMENT_METHOD="pm2"
    print_status "PM2 detected. Using PM2 for process management."
elif systemctl --version &> /dev/null; then
    DEPLOYMENT_METHOD="systemd"
    print_status "systemd detected. Using systemd for process management."
else
    DEPLOYMENT_METHOD="manual"
    print_warning "No process manager detected. Manual deployment mode."
fi

# Deploy based on available method
case $DEPLOYMENT_METHOD in
    "pm2")
        print_status "Deploying with PM2..."
        
        # Stop existing process if running
        pm2 stop $APP_NAME 2>/dev/null || true
        pm2 delete $APP_NAME 2>/dev/null || true
        
        # Start with ecosystem config
        pm2 start $PM2_ECOSYSTEM --env production
        
        # Save PM2 configuration
        pm2 save
        
        # Setup PM2 startup script
        print_status "Setting up PM2 startup script..."
        pm2 startup || print_warning "Could not setup PM2 startup script. You may need to run this manually with sudo."
        
        print_status "Application deployed with PM2!"
        print_status "Use 'pm2 status' to check application status"
        print_status "Use 'pm2 logs $APP_NAME' to view logs"
        print_status "Use 'pm2 restart $APP_NAME' to restart"
        ;;
        
    "systemd")
        print_status "Deploying with systemd..."
        
        # Copy service file (requires sudo)
        if [[ $EUID -ne 0 ]]; then
            print_warning "systemd deployment requires sudo privileges"
            sudo cp $SERVICE_NAME.service /etc/systemd/system/
            sudo systemctl daemon-reload
            sudo systemctl enable $SERVICE_NAME
            sudo systemctl restart $SERVICE_NAME
        else
            cp $SERVICE_NAME.service /etc/systemd/system/
            systemctl daemon-reload
            systemctl enable $SERVICE_NAME
            systemctl restart $SERVICE_NAME
        fi
        
        print_status "Application deployed with systemd!"
        print_status "Use 'sudo systemctl status $SERVICE_NAME' to check status"
        print_status "Use 'sudo journalctl -u $SERVICE_NAME -f' to view logs"
        print_status "Use 'sudo systemctl restart $SERVICE_NAME' to restart"
        ;;
        
    "manual")
        print_status "Manual deployment mode..."
        print_warning "Starting application manually. Consider installing PM2 or using systemd for production."
        
        # Kill existing process if running
        pkill -f "node.*dist/index.js" || true
        
        # Start application in background
        nohup node dist/index.js > logs/app.log 2>&1 &
        APP_PID=$!
        
        print_status "Application started with PID: $APP_PID"
        print_status "Logs are written to: logs/app.log"
        print_status "To stop: kill $APP_PID"
        ;;
esac

# Wait a moment for startup
sleep 5

# Test if application is responding
print_status "Testing application health..."
if command -v curl &> /dev/null; then
    if curl -f http://localhost:8765/health > /dev/null 2>&1; then
        print_status "✅ Application is responding to health checks!"
    else
        print_warning "⚠️  Application may not be responding. Check logs."
    fi
else
    print_warning "curl not available. Cannot test application health."
fi

print_status "🎉 Deployment completed!"
print_status ""
print_status "Next steps:"
print_status "1. Check application logs for any errors"
print_status "2. Test your API endpoints"
print_status "3. Monitor the application for stability"
print_status "4. Set up monitoring alerts if needed"
