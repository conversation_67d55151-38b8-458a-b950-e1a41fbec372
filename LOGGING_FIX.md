# EPIPE Error Fix - Comprehensive Logging Protection

## Problem
The production server was experiencing EPIPE (Broken Pipe) errors that caused server crashes. The error occurred when <PERSON>'s console transport tried to write to stdout/stderr when the pipe was broken. This typically happens when:

- The process is running as a daemon and stdout/stderr are redirected to /dev/null
- Process managers disconnect the console output streams
- The parent process reading stdout/stderr has died
- Terminal sessions are disconnected

The specific error was:
```
Error: write EPIPE
    at Console.log (/path/to/winston/lib/winston/transports/console.js:87:23)
```

This created a crash loop where:
1. An error occurs
2. Error handler tries to log it using console output
3. Console output triggers EPIPE error
4. Uncaught exception handler tries to log the EPIPE error
5. This triggers another EPIPE error, causing a crash

## Comprehensive Solution Implemented

### 1. Complete Console Transport Elimination in Production
- Console transport is **completely disabled** in production environments
- Console transport only enabled in development AND when `ENABLE_CONSOLE_LOGGING=true`
- This prevents ANY Winston console output in production

### 2. Bulletproof Safe Logger Wrapper
- Created comprehensive error handling wrapper around ALL logger methods
- Catches EPIPE, ENOTCONN, and ECONNRESET errors and silently ignores them
- Includes fallback logging to dedicated error files when <PERSON> fails
- Added emergency logging method that bypasses <PERSON> entirely

### 3. Process-Level EPIPE Protection
- Added SIGPIPE signal handler to prevent process crashes
- Added stdout/stderr error handlers to catch pipe errors
- Enhanced uncaught exception handler to ignore all pipe-related errors
- Removed ALL console.error calls from error handlers

### 4. Transport Error Handling
- File transport errors write to fallback files instead of console
- Multiple layers of error handling to prevent cascading failures
- Emergency logging system for when all else fails

### 5. Complete Console Output Elimination
- Removed all console.error, console.log calls from error handlers
- SIGTERM handler uses file-only logging
- WebSocket error handlers use file-only logging
- Uncaught exception handlers use file-only logging

## Environment Variables

**Critical for Production:**
```bash
# REQUIRED: Set production environment
NODE_ENV=production

# REQUIRED: Disable console logging to prevent EPIPE errors
ENABLE_CONSOLE_LOGGING=false

# Optional: Configure restart behavior
AUTO_RESTART_ENABLED=true
MAX_RESTARTS=5
RESTART_WINDOW_MS=300000
```

## Files Modified

1. **src/shared/lib/logger.ts**
   - Removed console transport in production
   - Enhanced safe logger wrapper
   - Added emergency logging system
   - Improved transport error handling

2. **src/index.ts**
   - Added comprehensive EPIPE protection
   - Removed console.error from error handlers
   - Enhanced uncaught exception handling
   - Added stdout/stderr error handlers

3. **src/features/websocket/server.ts**
   - Removed console.error from WebSocket error handler
   - Added emergency logging fallback

## Testing

Run the test script to verify the fix:
```bash
# Build the project first
npm run build

# Run the logging test
node test-logging.js
```

## Deployment Steps

1. **Update your production environment**:
   - The `.env.production` file has been updated with `ENABLE_CONSOLE_LOGGING=false`

2. **Rebuild and deploy**:
   ```bash
   npm run build
   # Deploy your built application
   ```

3. **Restart your production server**:
   - If using PM2: `pm2 restart your-app-name`
   - If using systemd: `sudo systemctl restart your-service`
   - If using Docker: rebuild and restart your container

4. **Monitor logs**:
   - Check that the application starts without EPIPE errors
   - Logs will still be written to files in the `logs/` directory
   - Console output will be minimal in production

## Log Files Location

Logs are written to:
- `logs/error-YYYY-MM-DD.log` - Error level logs only
- `logs/combined-YYYY-MM-DD.log` - All log levels
- Files rotate daily and are compressed after 14 days

## Troubleshooting

If you still experience issues:

1. **Check file permissions**: Ensure the application can write to the `logs/` directory
2. **Disk space**: Ensure sufficient disk space for log files
3. **Re-enable console logging temporarily**: Set `ENABLE_CONSOLE_LOGGING=true` for debugging
4. **Check process manager logs**: Look at PM2/systemd logs for additional context

## Reverting Changes

If you need to revert to console logging:
1. Set `ENABLE_CONSOLE_LOGGING=true` in your environment
2. Restart the application

The safe logger wrapper will still protect against EPIPE errors even with console logging enabled.
