{"name": "liteserver", "version": "1.0.0", "description": "Express server with MySQL and logging", "main": "src/index.ts", "scripts": {"start": "node dist/index.js", "dev": "nodemon --exec ts-node src/index.ts", "build": "tsc && npm run copy-static", "copy-static": "node -e \"require('fs-extra').copySync('src/public', 'dist/public')\"", "lint": "eslint . --ext .ts", "format": "prettier --write \"src/**/*.{ts,js}\"", "format:check": "prettier --check \"src/**/*.{ts,js}\"", "test": "vitest run --config vitest.config.mjs", "test:watch": "vitest --config vitest.config.mjs", "test:db": "npm run build && node scripts/test-db-connection.js", "test:graceful": "npm run build && node scripts/test-graceful-startup.js", "load-test": "artillery run load-tests/artillery-config.yml", "load-test:report": "artillery run load-tests/artillery-config.yml --output report.json", "load-test:verbose": "artillery run -v load-tests/artillery-config.yml", "load-test:debug": "set DEBUG=artillery:* && artillery run load-tests/artillery-config.yml", "load-test:debug-powershell": "cross-env DEBUG=artillery:* artillery run load-tests/artillery-config.yml", "pm2:start": "pm2 start ecosystem.config.js --env production", "pm2:stop": "pm2 stop liteserver", "pm2:restart": "pm2 restart liteserver", "pm2:logs": "pm2 logs liteserver", "pm2:status": "pm2 status", "pm2:delete": "pm2 delete liteserver", "health": "curl -s http://localhost:8765/health | json_pp", "deploy": "npm run build && npm run pm2:start", "deploy:systemd": "npm run build && sudo systemctl restart liteserver"}, "keywords": [], "author": "", "license": "ISC", "type": "commonjs", "dependencies": {"@types/express-session": "^1.18.2", "artillery": "^2.0.23", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^5.1.0", "express-rate-limit": "^7.5.0", "express-session": "^1.18.1", "helmet": "^8.1.0", "jsonwebtoken": "^9.0.2", "mysql2": "^3.14.1", "passport": "^0.7.0", "passport-google-oauth20": "^2.0.0", "socket.io": "^4.7.4", "winston": "^3.17.0", "winston-daily-rotate-file": "^5.0.0", "ws": "^8.16.0", "axios": "^1.6.0", "cheerio": "^1.0.0-rc.12"}, "devDependencies": {"@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/fs-extra": "^11.0.4", "@types/jsonwebtoken": "^9.0.10", "@types/node": "^20.11.24", "@types/passport": "^1.0.16", "@types/passport-google-oauth20": "^2.0.14", "@types/ws": "^8.5.10", "@typescript-eslint/eslint-plugin": "^7.0.2", "@typescript-eslint/parser": "^7.0.2", "eslint": "^8.57.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.1.3", "fs-extra": "^11.2.0", "lint-staged": "^15.2.2", "nodemon": "^3.1.10", "prettier": "^3.2.5", "ts-node": "^10.9.2", "typescript": "^5.3.3", "vitest": "^1.3.1", "@types/cheerio": "^0.22.31"}, "lint-staged": {"*.{js,ts}": ["prettier --write", "eslint --fix"]}}