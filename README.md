# LiteServer

Express server with MySQL, WebSockets, and comprehensive logging.

## Features

- Express API server with modular routing
- Socket.IO WebSocket support
- MySQL database integration
- Winston logging with daily rotation
- Security with Helmet and rate limiting
- TypeScript support
- Testing with Vitest
- Load testing with Artillery

## Getting Started

### Prerequisites

- Node.js (v16+)
- npm or yarn
- MySQL server (optional, depending on configuration)

### Installation

1. Clone the repository
2. Install dependencies:
   ```
   npm install
   ```
3. Create a `.env` file based on the example provided

### Running the Server

Development mode:
```
npm run dev
```

Production build:
```
npm run build
npm start
```

## API Endpoints

The server provides various API endpoints organized by feature:

- `/api/competitions` - Competition data
- `/api/schedules` - Competition schedules
- `/api/entries` - Competition entries
- `/api/teams` - Team information
- `/api/results` - Competition results
- `/api/athletes` - Athlete information
- `/api/admin` - Admin functionality

## WebSocket Support

The server includes Socket.IO for real-time communication. Test clients are available at:

- `/ws-client` - Basic WebSocket client
- `/admin-dashboard` - Admin dashboard with socket and cache information

## Testing

Run tests:
```
npm test
```

Watch mode:
```
npm run test:watch
```

## Load Testing

The project includes Artillery configuration for load testing:

```
npm run load-test
npm run load-test:report
```

Custom socket load testing is also available in the `load-tests` directory.

## Deployment to IONOS Plesk

### Prerequisites
- IONOS hosting account with Plesk access
- Node.js installed on your Plesk server (v16+)
- MySQL database created in Plesk

### Deployment Steps

1. **Access Plesk Control Panel**
   - Log in to your IONOS Plesk control panel

2. **Create a Node.js Application**
   - Navigate to "Websites & Domains"
   - Select your domain
   - Click "Node.js" in the sidebar
   - Click "Add Node.js Application"
   - Configure the application:
     - Name: LiteServer
     - Document root: Select your domain's root or a subdirectory
     - Application mode: Production
     - Application URL: Choose your preferred URL path
     - Node.js version: Select v16 or higher

3. **Upload Your Code**
   - Use FTP or Git to upload your code to the specified document root
   - Alternatively, use the File Manager in Plesk to upload a ZIP archive

4. **Configure Environment Variables**
   - In the Node.js application settings, add the required environment variables:
     - NODE_ENV: production
     - APP_PORT: 8765 (or your preferred port)
     - DB_HOST, DB_PORT, DB_USER, DB_PASSWORD, DB_NAME (from your Plesk MySQL database)
     - Other required variables from your .env.production file

5. **Install Dependencies and Build**
   - Connect to your server via SSH
   - Navigate to your application directory
   - Run:
     ```
     npm install production
     ```

6. **Configure Application Entry Point**
   - In Plesk Node.js settings, set:
     - Application startup file: dist/index.js
     - Command line arguments: (leave empty)

7. **Configure Proxy Settings**
   - Enable "Proxy to the application" in Node.js application settings
   - Set the proxy port to match your APP_PORT (e.g., 8765)

8. **Start the Application**
   - Click "Start" or "Restart" in the Node.js application settings

9. **Set Up Process Management (Optional)**
   - Consider using PM2 for better process management:
     ```
     npm install -g pm2
     pm2 start index.js --name liteserver
     pm2 save
     ```
   - Configure PM2 to start on server reboot

10. **Verify Deployment**
    - Visit your domain to ensure the application is running
    - Test API endpoints and WebSocket functionality

## License

ISC
