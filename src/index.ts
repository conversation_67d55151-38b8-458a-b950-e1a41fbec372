// Load environment variables FIRST, before any other imports
import dotenv from 'dotenv';

// Configure dotenv at the very beginning
dotenv.config({
  path: process.env.NODE_ENV === 'production' ? '.env.production' : '.env',
});

// Log environment variables to verify they're loaded
console.log('Environment variables loaded:');
console.log('NODE_ENV:', process.env.NODE_ENV);
console.log('PORT:', process.env.PORT);
console.log('APP_PORT:', process.env.APP_PORT);
console.log('DB_HOST:', process.env.DB_HOST);
console.log('DB_PORT:', process.env.DB_PORT);
console.log('DB_USER:', process.env.DB_USER);
console.log('DB_NAME:', process.env.DB_NAME);
console.log('DB_CONNECTION_LIMIT:', process.env.DB_CONNECTION_LIMIT);
console.log('ALLOWED_DOMAINS:', process.env.ALLOWED_DOMAINS);

// Now import the rest of your modules
import express from 'express';
import http from 'http';
import { app } from './app';
import { logger } from './shared/lib/logger';
import { dbConnect } from './shared/lib/db';
import { WebSocketServer } from './features/websocket';
import { competitionRefresher } from './features/competitions/services/competition-refresher';
import { queueCleanupService } from './features/competitions/services/queue-cleanup-service';
import { restartManager } from './shared/lib/restart-manager';
import { healthMonitor } from './shared/lib/health-monitor';
import { a } from 'vitest/dist/reporters-w_64AS5f';

// Log all registered routes
console.log('Registered routes:');
const printRoutes = (stack: any[], basePath = '') => {
  stack.forEach(layer => {
    if (layer.route) {
      const path = basePath + layer.route.path;
      console.log(`Route: ${path}`);
    } else if (layer.name === 'router' && layer.handle.stack) {
      const path = layer.regexp.toString().includes('^\\/')
        ? basePath + '/' + layer.regexp.toString().split('^\\/')[1].split('\\/?')[0]
        : basePath;
      printRoutes(layer.handle.stack, path);
    }
  });
};

// Create HTTP server from Express app
const server = http.createServer(app);

// Initialize WebSocket server with the HTTP server
const wsServer = new WebSocketServer(
  server,
  process.env.EXTERNAL_WS_URL || 'wss://m9f8tsofz8.execute-api.eu-west-2.amazonaws.com/Prod'
);

// If you need to access it globally
(global as any).wsServer = wsServer;

// Simple URL logger
app.use((req, res, next) => {
  console.log(`index.ts URL: ${req.method} ${req.url}`);
  next();
});

// Print routes after app is fully configured
if (app._router && app._router.stack) {
  console.log('Printing all registered routes:');
  printRoutes(app._router.stack);
} else {
  console.log('No routes registered yet or router not initialized');
}

// Global database and WordPress connection state
let isDatabaseConnected = false;
let isWordPressConnected = false;

// Start the server with optional database and WordPress connections
const startServer = async () => {
  const APP_PORT = process.env.APP_PORT || 3000;
  const requireDbOnStartup = process.env.REQUIRE_DB_ON_STARTUP !== 'false';
  const requireWpOnStartup = process.env.REQUIRE_WP_ON_STARTUP !== 'false';
  const wpConnectionEnabled = process.env.WP_CONNECTION_ENABLED !== 'false';

  // Always start the HTTP server first
  server.listen(APP_PORT, () => {
    console.log(`Server running on port ${APP_PORT}`);
    logger.info(`Server running on port ${APP_PORT}`);
  });

  // Test database connection
  try {
    logger.info('Testing database connection...');
    const maxRetries = parseInt(process.env.DB_STARTUP_RETRIES || '5', 10);
    const retryDelay = parseInt(process.env.DB_STARTUP_RETRY_DELAY || '2000', 10);

    await dbConnect.testConnectionWithRetry(maxRetries, retryDelay);
    isDatabaseConnected = true;
    logger.info('Database connection successful');
  } catch (error) {
    isDatabaseConnected = false;
    logger.error('Database connection failed during startup:', error);

    if (requireDbOnStartup) {
      logger.error('REQUIRE_DB_ON_STARTUP is true, shutting down server...');
      server.close(() => {
        restartManager.handleStartupFailure(`Database connection failed: ${error}`);
      });
      return;
    } else {
      logger.warn('Server starting without database connection - some features may be unavailable');
      startDatabaseReconnectionLoop();
    }
  }

  // Test WordPress connection only if enabled
  if (wpConnectionEnabled) {
    try {
      logger.info('Testing WordPress connection...');
      const { wpAuthService } = await import('./features/wordpress/services/wp-auth');
      await wpAuthService.authenticate();

      // Perform a basic test to verify the connection works
      const testResponse = await wpAuthService.makeAuthenticatedRequest(
        `${process.env.WP_API_BASE_URL}/users/me`
      );
      isWordPressConnected = true;
      logger.info('WordPress connection successful', {
        userId: testResponse.data.id,
        username: testResponse.data.username,
        roles: testResponse.data.roles,
        email: testResponse.data.email,
      });
    } catch (error: any) {
      isWordPressConnected = false;
      logger.error('WordPress connection failed during startup:', {
        error: error instanceof Error ? error.message : String(error),
        status: error.response?.status,
        statusText: error.response?.statusText,
        data: error.response?.data,
      });

      if (requireWpOnStartup) {
        logger.error('REQUIRE_WP_ON_STARTUP is true, shutting down server...');
        server.close(() => {
          restartManager.handleStartupFailure(`WordPress connection failed: ${error}`);
        });
        return;
      } else {
        logger.warn(
          'Server starting without WordPress connection - WordPress features may be unavailable'
        );
        startWordPressReconnectionLoop();
      }
    }
  } else {
    logger.info('WordPress connection disabled via WP_CONNECTION_ENABLED=false');
    isWordPressConnected = false;
  }
};

// Background database reconnection loop
const startDatabaseReconnectionLoop = () => {
  const reconnectInterval = parseInt(process.env.DB_RECONNECT_INTERVAL_MS || '30000', 10); // 30 seconds

  const attemptReconnection = async () => {
    if (isDatabaseConnected) {
      return; // Already connected
    }

    try {
      logger.info('Attempting database reconnection...');
      await dbConnect.testConnection();
      isDatabaseConnected = true;
      logger.info('Database reconnection successful!');

      // Restart services that depend on database
      if (isDatabaseConnected) {
        logger.info('Restarting database-dependent services...');
        try {
          competitionRefresher.start();
          queueCleanupService.start();
        } catch (error) {
          logger.warn('Failed to restart some services:', error);
        }
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      logger.debug(`Database reconnection failed: ${errorMessage}`);
      // Continue trying in background
    }
  };

  // Initial attempt after a short delay
  setTimeout(attemptReconnection, 5000);

  // Then retry periodically
  setInterval(attemptReconnection, reconnectInterval);

  logger.info(`Database reconnection loop started (interval: ${reconnectInterval}ms)`);
};

// Background WordPress reconnection loop
const startWordPressReconnectionLoop = () => {
  const wpConnectionEnabled = process.env.WP_CONNECTION_ENABLED !== 'false';

  if (!wpConnectionEnabled) {
    logger.info('WordPress reconnection loop disabled via WP_CONNECTION_ENABLED=false');
    return;
  }

  const reconnectInterval = parseInt(process.env.WP_RECONNECT_INTERVAL_MS || '60000', 10); // 60 seconds

  const attemptReconnection = async () => {
    if (isWordPressConnected) {
      return; // Already connected
    }

    try {
      logger.info('Attempting WordPress reconnection...');
      const { wpAuthService } = await import('./features/wordpress/services/wp-auth');
      await wpAuthService.authenticate();

      // Perform a basic test to verify the connection works
      try {
        const testResponse = await wpAuthService.makeAuthenticatedRequest(
          `${process.env.WP_API_BASE_URL}/users/me`
        );
        isWordPressConnected = true;
        logger.info('WordPress reconnection successful!', {
          userId: testResponse.data.id,
          username: testResponse.data.username,
          roles: testResponse.data.roles,
          email: testResponse.data.email,
        });
      } catch (testError: any) {
        logger.warn('WordPress authentication succeeded but API test failed:', {
          error: testError instanceof Error ? testError.message : String(testError),
          status: testError.response?.status,
          statusText: testError.response?.statusText,
        });
        // Don't set isWordPressConnected = true if the test fails
      }
    } catch (error: any) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      logger.debug(`WordPress reconnection failed: ${errorMessage}`, {
        status: error.response?.status,
        statusText: error.response?.statusText,
        url: process.env.WP_LOGIN_URL,
      });
      // Continue trying in background
    }
  };

  // Initial attempt after a short delay
  setTimeout(attemptReconnection, 10000);

  // Then retry periodically
  setInterval(attemptReconnection, reconnectInterval);

  logger.info(`WordPress reconnection loop started (interval: ${reconnectInterval}ms)`);
};

// Export connection states for use in other modules
export const getDatabaseConnectionState = () => isDatabaseConnected;
export const getWordPressConnectionState = () => isWordPressConnected;

startServer().then(() => {
  // Start database-dependent services only if database is connected
  if (isDatabaseConnected) {
    // Start the competition refresher
    let refreshIntervalMs = parseInt(process.env.COMPETITION_REFRESH_INTERVAL_MS || '10000', 10);

    // don't let it go below 3 seconds
    if (refreshIntervalMs < 3000) {
      logger.warn(`Refresh interval too low: ${refreshIntervalMs}ms, setting to 3000ms`);
      refreshIntervalMs = 3000;
    }

    logger.info(`Starting competition refresher with interval: ${refreshIntervalMs}ms`);
    competitionRefresher.setRefreshInterval(refreshIntervalMs);
    competitionRefresher.start();

    // Start queue cleanup service
    queueCleanupService.start();
  } else {
    logger.warn('Database-dependent services not started - database connection unavailable');
  }

  // Always start health monitoring (it can handle database being down)
  healthMonitor.start();

  // Reset restart counter after successful startup
  setTimeout(() => {
    restartManager.resetRestartCount();
    logger.info('Application startup completed successfully');
  }, 30000); // Wait 30 seconds to ensure stable startup
});

// Comprehensive EPIPE and pipe error handling
process.on('SIGPIPE', () => {
  // Silently ignore SIGPIPE signals
  // This prevents the process from crashing when stdout/stderr is closed
  // Common in daemon environments where output is redirected to /dev/null
});

// Handle stdout/stderr errors to prevent EPIPE crashes
if (process.stdout) {
  process.stdout.on('error', (error: any) => {
    if (error.code === 'EPIPE' || error.code === 'ENOTCONN') {
      // Silently ignore stdout pipe errors
      return;
    }
    // Log other stdout errors to file only
    try {
      logger.emergency('stdout error', { error: error.message, code: error.code });
    } catch {
      // If even emergency logging fails, silently continue
    }
  });
}

if (process.stderr) {
  process.stderr.on('error', (error: any) => {
    if (error.code === 'EPIPE' || error.code === 'ENOTCONN') {
      // Silently ignore stderr pipe errors
      return;
    }
    // Log other stderr errors to file only
    try {
      logger.emergency('stderr error', { error: error.message, code: error.code });
    } catch {
      // If even emergency logging fails, silently continue
    }
  });
}

// Handle uncaught exceptions
process.on('uncaughtException', async error => {
  // Check if it's an EPIPE error and handle it gracefully
  if ((error as any).code === 'EPIPE' || (error as any).code === 'ENOTCONN' || (error as any).code === 'ECONNRESET') {
    // Silently ignore pipe/connection errors - don't log as it can trigger another EPIPE
    return; // Don't exit the process for pipe errors
  }

  // For non-EPIPE errors, log using ONLY file-based logging (no console output)
  try {
    logger.error('Uncaught Exception:', {
      message: error.message,
      stack: error.stack,
      code: (error as any).code,
      errno: (error as any).errno,
      syscall: (error as any).syscall,
      timestamp: new Date().toISOString()
    });
  } catch (logError) {
    // If even file logging fails, use emergency logging
    try {
      logger.emergency('Failed to log uncaught exception', {
        originalError: error.message,
        logError: (logError as any).message
      });
    } catch {
      // If everything fails, there's nothing more we can do safely
    }
  }

  // Attempt restart instead of immediate exit
  const restarted = await restartManager.attemptRestart(`Uncaught exception: ${error.message}`);
  if (!restarted) {
    process.exit(1);
  }
});

// Handle unhandled promise rejections
process.on('unhandledRejection', async (reason, promise) => {
  // NO console output - use only file-based logging to prevent EPIPE errors
  try {
    logger.error('Unhandled Rejection', {
      reason: typeof reason === 'string' ? reason : JSON.stringify(reason),
      promise: promise?.toString() || 'Unknown promise',
      timestamp: new Date().toISOString(),
    });
  } catch (logError) {
    // If file logging fails, use emergency logging
    try {
      logger.emergency('Failed to log unhandled rejection', {
        originalReason: typeof reason === 'string' ? reason : 'Complex object',
        logError: (logError as any).message
      });
    } catch {
      // If everything fails, silently continue to prevent crashes
    }
  }

  // Attempt restart instead of immediate exit
  const restarted = await restartManager.attemptRestart(`Unhandled rejection: ${reason}`);
  if (!restarted) {
    // If restart failed or not allowed, exit
    process.exit(1);
  }
});

// Handle graceful shutdown signals
process.on('SIGTERM', async () => {
  // NO console output - use only file-based logging to prevent EPIPE errors
  try {
    logger.info('SIGTERM received, shutting down gracefully');
  } catch (logError) {
    // If file logging fails, use emergency logging
    try {
      logger.emergency('Failed to log SIGTERM', { error: (logError as any).message });
    } catch {
      // If everything fails, silently continue
    }
  }

  // Stop health monitoring
  healthMonitor.stop();

  // Stop competition refresher
  competitionRefresher.stop();

  // Stop queue cleanup service
  queueCleanupService.stop();

  process.exit(0);
});

process.on('SIGINT', async () => {
  console.log('SIGINT received, shutting down gracefully');
  try {
    logger.info('SIGINT received, shutting down gracefully');
  } catch (logError) {
    console.error('Failed to log SIGINT:', logError);
  }

  // Stop health monitoring
  healthMonitor.stop();

  // Stop competition refresher
  competitionRefresher.stop();

  // Stop queue cleanup service
  queueCleanupService.stop();

  process.exit(0);
});
