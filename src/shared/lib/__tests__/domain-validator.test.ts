import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { DomainValidator } from '../domain-validator';

describe('DomainValidator', () => {
  let originalEnv: string | undefined;

  beforeEach(() => {
    // Store original environment variable
    originalEnv = process.env.ALLOWED_DOMAINS;
  });

  afterEach(() => {
    // Restore original environment variable
    if (originalEnv !== undefined) {
      process.env.ALLOWED_DOMAINS = originalEnv;
    } else {
      delete process.env.ALLOWED_DOMAINS;
    }
  });

  describe('constructor and loadAllowedDomains', () => {
    it('should load domains from environment variable', () => {
      process.env.ALLOWED_DOMAINS = 'entry4sports.co.uk,uat.entry4sports.co.uk';
      const validator = new DomainValidator();
      
      expect(validator.getAllowedDomains()).toEqual([
        'entry4sports.co.uk',
        'uat.entry4sports.co.uk'
      ]);
    });

    it('should handle empty environment variable', () => {
      process.env.ALLOWED_DOMAINS = '';
      const validator = new DomainValidator();
      
      expect(validator.getAllowedDomains()).toEqual([]);
    });

    it('should handle undefined environment variable', () => {
      delete process.env.ALLOWED_DOMAINS;
      const validator = new DomainValidator();
      
      expect(validator.getAllowedDomains()).toEqual([]);
    });

    it('should trim and normalize domains', () => {
      process.env.ALLOWED_DOMAINS = ' Entry4Sports.co.uk , UAT.entry4sports.co.uk ';
      const validator = new DomainValidator();
      
      expect(validator.getAllowedDomains()).toEqual([
        'entry4sports.co.uk',
        'uat.entry4sports.co.uk'
      ]);
    });

    it('should filter out empty domains', () => {
      process.env.ALLOWED_DOMAINS = 'entry4sports.co.uk,,uat.entry4sports.co.uk,';
      const validator = new DomainValidator();
      
      expect(validator.getAllowedDomains()).toEqual([
        'entry4sports.co.uk',
        'uat.entry4sports.co.uk'
      ]);
    });
  });

  describe('isAllowed', () => {
    it('should allow domains in the allowed list', () => {
      process.env.ALLOWED_DOMAINS = 'entry4sports.co.uk,uat.entry4sports.co.uk';
      const validator = new DomainValidator();
      
      expect(validator.isAllowed('entry4sports.co.uk')).toBe(true);
      expect(validator.isAllowed('uat.entry4sports.co.uk')).toBe(true);
    });

    it('should reject domains not in the allowed list', () => {
      process.env.ALLOWED_DOMAINS = 'entry4sports.co.uk';
      const validator = new DomainValidator();
      
      expect(validator.isAllowed('uat.entry4sports.co.uk')).toBe(false);
      expect(validator.isAllowed('malicious.com')).toBe(false);
      expect(validator.isAllowed('example.com')).toBe(false);
    });

    it('should handle case insensitive matching', () => {
      process.env.ALLOWED_DOMAINS = 'entry4sports.co.uk';
      const validator = new DomainValidator();
      
      expect(validator.isAllowed('Entry4Sports.co.uk')).toBe(true);
      expect(validator.isAllowed('ENTRY4SPORTS.CO.UK')).toBe(true);
      expect(validator.isAllowed('entry4SPORTS.co.UK')).toBe(true);
    });

    it('should trim whitespace from input domain', () => {
      process.env.ALLOWED_DOMAINS = 'entry4sports.co.uk';
      const validator = new DomainValidator();
      
      expect(validator.isAllowed(' entry4sports.co.uk ')).toBe(true);
      expect(validator.isAllowed('\tentry4sports.co.uk\n')).toBe(true);
    });

    it('should reject empty or undefined domains', () => {
      process.env.ALLOWED_DOMAINS = 'entry4sports.co.uk';
      const validator = new DomainValidator();
      
      expect(validator.isAllowed('')).toBe(false);
      expect(validator.isAllowed('   ')).toBe(false);
      expect(validator.isAllowed(undefined as any)).toBe(false);
      expect(validator.isAllowed(null as any)).toBe(false);
    });

    it('should allow all domains when no domains are configured', () => {
      delete process.env.ALLOWED_DOMAINS;
      const validator = new DomainValidator();
      
      expect(validator.isAllowed('entry4sports.co.uk')).toBe(true);
      expect(validator.isAllowed('uat.entry4sports.co.uk')).toBe(true);
      expect(validator.isAllowed('malicious.com')).toBe(true);
    });

    it('should allow all domains when empty domains are configured', () => {
      process.env.ALLOWED_DOMAINS = '';
      const validator = new DomainValidator();
      
      expect(validator.isAllowed('entry4sports.co.uk')).toBe(true);
      expect(validator.isAllowed('malicious.com')).toBe(true);
    });
  });

  describe('reload', () => {
    it('should reload domains from environment variable', () => {
      process.env.ALLOWED_DOMAINS = 'entry4sports.co.uk';
      const validator = new DomainValidator();
      
      expect(validator.getAllowedDomains()).toEqual(['entry4sports.co.uk']);
      
      // Change environment variable
      process.env.ALLOWED_DOMAINS = 'entry4sports.co.uk,uat.entry4sports.co.uk';
      validator.reload();
      
      expect(validator.getAllowedDomains()).toEqual([
        'entry4sports.co.uk',
        'uat.entry4sports.co.uk'
      ]);
    });
  });

  describe('getAllowedDomains', () => {
    it('should return a copy of allowed domains array', () => {
      process.env.ALLOWED_DOMAINS = 'entry4sports.co.uk,uat.entry4sports.co.uk';
      const validator = new DomainValidator();
      
      const domains1 = validator.getAllowedDomains();
      const domains2 = validator.getAllowedDomains();
      
      expect(domains1).toEqual(domains2);
      expect(domains1).not.toBe(domains2); // Should be different array instances
      
      // Modifying returned array should not affect validator
      domains1.push('malicious.com');
      expect(validator.getAllowedDomains()).toEqual([
        'entry4sports.co.uk',
        'uat.entry4sports.co.uk'
      ]);
    });
  });
});
