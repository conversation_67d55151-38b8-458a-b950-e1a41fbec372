import { logger } from './logger';

/**
 * Domain validation utility for filtering websocket messages
 */
export class DomainValidator {
  private allowedDomains: Set<string>;

  constructor() {
    this.allowedDomains = this.loadAllowedDomains();
  }

  /**
   * Load allowed domains from environment variable
   * @private
   */
  private loadAllowedDomains(): Set<string> {
    const domainsEnv = process.env.ALLOWED_DOMAINS;
    
    if (!domainsEnv) {
      logger.warn('ALLOWED_DOMAINS environment variable not set. No domain filtering will be applied.');
      return new Set();
    }

    const domains = domainsEnv
      .split(',')
      .map(domain => domain.trim().toLowerCase())
      .filter(domain => domain.length > 0);

    if (domains.length === 0) {
      logger.warn('ALLOWED_DOMAINS environment variable is empty. No domain filtering will be applied.');
      return new Set();
    }

    logger.info(`Loaded ${domains.length} allowed domains: ${domains.join(', ')}`);
    return new Set(domains);
  }

  /**
   * Check if a domain is allowed
   * @param domain - The domain to validate
   * @returns true if the domain is allowed, false otherwise
   */
  public isAllowed(domain: string): boolean {
    if (this.allowedDomains.size === 0) {
      // If no domains are configured, allow all (for backward compatibility)
      return true;
    }

    if (!domain) {
      logger.warn('Message received with empty or undefined domain');
      return false;
    }

    const normalizedDomain = domain.trim().toLowerCase();
    const isAllowed = this.allowedDomains.has(normalizedDomain);

    if (!isAllowed) {
      logger.info(`Message from domain '${domain}' rejected - not in allowed domains list`);
    }

    return isAllowed;
  }

  /**
   * Get the list of allowed domains
   * @returns Array of allowed domains
   */
  public getAllowedDomains(): string[] {
    return Array.from(this.allowedDomains);
  }

  /**
   * Reload allowed domains from environment (useful for testing or runtime config changes)
   */
  public reload(): void {
    this.allowedDomains = this.loadAllowedDomains();
  }
}

// Export a singleton instance
export const domainValidator = new DomainValidator();
