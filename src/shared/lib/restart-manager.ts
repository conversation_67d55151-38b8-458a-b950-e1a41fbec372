import { logger } from './logger';
import { spawn } from 'child_process';
import path from 'path';

interface RestartConfig {
  maxRestarts: number;
  restartWindow: number; // Time window in ms to count restarts
  initialDelay: number; // Initial delay before restart in ms
  maxDelay: number; // Maximum delay between restarts in ms
  backoffMultiplier: number; // Exponential backoff multiplier
  enabled: boolean;
}

class RestartManager {
  private restartCount = 0;
  private restartTimes: number[] = [];
  private isRestarting = false;
  private config: RestartConfig;

  constructor(config?: Partial<RestartConfig>) {
    this.config = {
      maxRestarts: parseInt(process.env.MAX_RESTARTS || '5', 10),
      restartWindow: parseInt(process.env.RESTART_WINDOW_MS || '300000', 10), // 5 minutes
      initialDelay: parseInt(process.env.RESTART_INITIAL_DELAY_MS || '1000', 10), // 1 second
      maxDelay: parseInt(process.env.RESTART_MAX_DELAY_MS || '60000', 10), // 1 minute
      backoffMultiplier: parseFloat(process.env.RESTART_BACKOFF_MULTIPLIER || '2'),
      enabled: process.env.AUTO_RESTART_ENABLED !== 'false', // Enabled by default
      ...config,
    };

    logger.info('RestartManager initialized with config:', this.config);
  }

  /**
   * Check if we should restart based on current restart count and timing
   */
  private shouldRestart(): boolean {
    if (!this.config.enabled) {
      logger.info('Auto-restart is disabled');
      return false;
    }

    if (this.isRestarting) {
      logger.warn('Restart already in progress');
      return false;
    }

    const now = Date.now();
    const windowStart = now - this.config.restartWindow;

    // Remove old restart times outside the window
    this.restartTimes = this.restartTimes.filter(time => time > windowStart);

    if (this.restartTimes.length >= this.config.maxRestarts) {
      logger.error(
        `Maximum restart limit reached: ${this.restartTimes.length}/${this.config.maxRestarts} ` +
        `restarts in the last ${this.config.restartWindow / 1000} seconds. Giving up.`
      );
      return false;
    }

    return true;
  }

  /**
   * Calculate delay before next restart using exponential backoff
   */
  private calculateDelay(): number {
    const delay = Math.min(
      this.config.initialDelay * Math.pow(this.config.backoffMultiplier, this.restartTimes.length),
      this.config.maxDelay
    );
    return delay;
  }

  /**
   * Attempt to restart the application
   */
  async attemptRestart(reason: string): Promise<boolean> {
    if (!this.shouldRestart()) {
      return false;
    }

    this.isRestarting = true;
    const now = Date.now();
    this.restartTimes.push(now);
    this.restartCount++;

    const delay = this.calculateDelay();

    logger.warn(
      `Attempting restart #${this.restartCount} due to: ${reason}. ` +
      `Waiting ${delay}ms before restart...`
    );

    try {
      // Wait for the calculated delay
      await new Promise(resolve => setTimeout(resolve, delay));

      // Log restart attempt
      logger.info(`Restarting application (attempt ${this.restartCount}/${this.config.maxRestarts})`);

      // Perform graceful cleanup before restart
      await this.performCleanup();

      // Restart the process
      this.restartProcess();

      return true;
    } catch (error) {
      logger.error('Failed to restart application:', error);
      this.isRestarting = false;
      return false;
    }
  }

  /**
   * Handle startup failures with restart logic
   */
  async handleStartupFailure(reason: string): Promise<void> {
    logger.error(`Startup failure: ${reason}`);

    const restarted = await this.attemptRestart(`Startup failure: ${reason}`);
    if (!restarted) {
      logger.error('Failed to restart after startup failure, exiting...');
      process.exit(1);
    }
  }

  /**
   * Perform cleanup before restart
   */
  private async performCleanup(): Promise<void> {
    try {
      logger.info('Performing cleanup before restart...');

      // Close database connections
      try {
        const { dbConnect } = await import('./db');
        if (dbConnect && typeof dbConnect.close === 'function') {
          await dbConnect.close();
          logger.info('Database connections closed');
        }
      } catch (error) {
        logger.warn('Could not close database connections:', error);
      }

      // Stop competition refresher
      try {
        const { competitionRefresher } = await import('../../features/competitions/services/competition-refresher');
        if (competitionRefresher && typeof competitionRefresher.stop === 'function') {
          competitionRefresher.stop();
          logger.info('Competition refresher stopped');
        }
      } catch (error) {
        logger.warn('Could not stop competition refresher:', error);
      }

      // Stop health monitor if available
      try {
        const { healthMonitor } = await import('./health-monitor');
        if (healthMonitor && typeof healthMonitor.stop === 'function') {
          healthMonitor.stop();
          logger.info('Health monitor stopped');
        }
      } catch (error) {
        logger.warn('Could not stop health monitor:', error);
      }

      // Give time for cleanup to complete
      await new Promise(resolve => setTimeout(resolve, 2000));

      logger.info('Cleanup completed');
    } catch (error) {
      logger.error('Error during cleanup:', error);
    }
  }

  /**
   * Restart the current process
   */
  private restartProcess(): void {
    const scriptPath = process.argv[1];
    const args = process.argv.slice(2);

    logger.info(`Restarting process: ${scriptPath} with args: ${args.join(' ')}`);

    // Spawn new process
    const child = spawn(process.execPath, [scriptPath, ...args], {
      detached: true,
      stdio: 'inherit',
      env: process.env,
    });

    child.unref();

    // Exit current process after a short delay
    setTimeout(() => {
      logger.info('Exiting current process for restart');
      process.exit(0);
    }, 2000);
  }

  /**
   * Reset restart counter (call this when app runs successfully for a while)
   */
  resetRestartCount(): void {
    const now = Date.now();
    const windowStart = now - this.config.restartWindow;

    // If we haven't restarted in the window, reset the counter
    this.restartTimes = this.restartTimes.filter(time => time > windowStart);

    if (this.restartTimes.length === 0) {
      this.restartCount = 0;
      logger.info('Restart counter reset - application running stable');
    }
  }

  /**
   * Get current restart statistics
   */
  getStats() {
    return {
      restartCount: this.restartCount,
      recentRestarts: this.restartTimes.length,
      isRestarting: this.isRestarting,
      config: this.config,
    };
  }
}

// Create singleton instance
const restartManager = new RestartManager();

export { RestartManager, restartManager };
