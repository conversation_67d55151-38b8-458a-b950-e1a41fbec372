import { logger } from './logger';
import { dbConnect } from './db';
import { restartManager } from './restart-manager';

interface HealthCheckResult {
  name: string;
  status: 'healthy' | 'unhealthy' | 'warning';
  message: string;
  timestamp: number;
  responseTime?: number;
}

interface HealthMonitorConfig {
  checkInterval: number; // How often to run health checks (ms)
  unhealthyThreshold: number; // How many consecutive failures before restart
  memoryThreshold: number; // Memory usage threshold (MB)
  enabled: boolean;
}

class HealthMonitor {
  private config: HealthMonitorConfig;
  private checkTimer: NodeJS.Timeout | null = null;
  private consecutiveFailures = 0;
  private lastHealthCheck: Date | null = null;
  private isRunning = false;

  constructor(config?: Partial<HealthMonitorConfig>) {
    this.config = {
      checkInterval: parseInt(process.env.HEALTH_CHECK_INTERVAL_MS || '30000', 10), // 30 seconds
      unhealthyThreshold: parseInt(process.env.HEALTH_UNHEALTHY_THRESHOLD || '3', 10), // 3 failures
      memoryThreshold: parseInt(process.env.HEALTH_MEMORY_THRESHOLD_MB || '512', 10), // 512 MB
      enabled: process.env.HEALTH_MONITOR_ENABLED !== 'false', // Enabled by default
      ...config,
    };

    logger.info('HealthMonitor initialized with config:', this.config);
  }

  /**
   * Start the health monitoring
   */
  start(): void {
    if (!this.config.enabled) {
      logger.info('Health monitoring is disabled');
      return;
    }

    if (this.isRunning) {
      logger.warn('Health monitor is already running');
      return;
    }

    this.isRunning = true;
    logger.info(`Starting health monitor with ${this.config.checkInterval}ms interval`);

    this.checkTimer = setInterval(() => {
      this.performHealthCheck();
    }, this.config.checkInterval);

    // Run initial health check
    this.performHealthCheck();
  }

  /**
   * Stop the health monitoring
   */
  stop(): void {
    if (this.checkTimer) {
      clearInterval(this.checkTimer);
      this.checkTimer = null;
    }
    this.isRunning = false;
    logger.info('Health monitor stopped');
  }

  /**
   * Perform comprehensive health check
   */
  private async performHealthCheck(): Promise<void> {
    try {
      const startTime = Date.now();
      const results: HealthCheckResult[] = [];

      // Check database connectivity
      results.push(await this.checkDatabase());

      // Check WordPress connectivity
      results.push(await this.checkWordPress());

      // Check memory usage
      results.push(this.checkMemoryUsage());

      // Check process uptime
      results.push(this.checkProcessUptime());

      // Check event loop lag
      results.push(await this.checkEventLoopLag());

      const endTime = Date.now();
      const totalTime = endTime - startTime;

      this.lastHealthCheck = new Date();

      // Analyze results
      const unhealthyChecks = results.filter(r => r.status === 'unhealthy');
      const warningChecks = results.filter(r => r.status === 'warning');

      if (unhealthyChecks.length > 0) {
        this.consecutiveFailures++;
        logger.warn(
          `Health check failed (${this.consecutiveFailures}/${this.config.unhealthyThreshold}): ` +
            `${unhealthyChecks.map(c => c.name).join(', ')}`
        );

        // Log detailed failure information
        unhealthyChecks.forEach(check => {
          logger.error(`Health check failure - ${check.name}: ${check.message}`);
        });

        // Check if we should restart
        if (this.consecutiveFailures >= this.config.unhealthyThreshold) {
          const failureReasons = unhealthyChecks.map(c => `${c.name}: ${c.message}`).join('; ');
          await restartManager.attemptRestart(`Health check failures: ${failureReasons}`);
        }
      } else {
        // Reset failure counter on successful health check
        if (this.consecutiveFailures > 0) {
          logger.info(`Health check recovered after ${this.consecutiveFailures} failures`);
          this.consecutiveFailures = 0;
        }

        // Log warnings if any
        if (warningChecks.length > 0) {
          warningChecks.forEach(check => {
            logger.warn(`Health check warning - ${check.name}: ${check.message}`);
          });
        }

        logger.debug(`Health check passed in ${totalTime}ms`);
      }
    } catch (error) {
      this.consecutiveFailures++;
      logger.error('Health check error:', error);

      if (this.consecutiveFailures >= this.config.unhealthyThreshold) {
        await restartManager.attemptRestart(`Health check system error: ${error}`);
      }
    }
  }

  /**
   * Check database connectivity
   */
  private async checkDatabase(): Promise<HealthCheckResult> {
    const startTime = Date.now();
    const requireDbOnStartup = process.env.REQUIRE_DB_ON_STARTUP !== 'false';

    try {
      await dbConnect.testConnection();
      const responseTime = Date.now() - startTime;

      return {
        name: 'database',
        status: responseTime > 5000 ? 'warning' : 'healthy',
        message:
          responseTime > 5000
            ? `Slow database response: ${responseTime}ms`
            : 'Database connection OK',
        timestamp: Date.now(),
        responseTime,
      };
    } catch (error) {
      // In graceful degradation mode, database failures are warnings, not unhealthy
      const status = requireDbOnStartup ? 'unhealthy' : 'warning';
      const message = requireDbOnStartup
        ? `Database connection failed: ${error}`
        : `Database unavailable (graceful degradation mode): ${error}`;

      return {
        name: 'database',
        status,
        message,
        timestamp: Date.now(),
        responseTime: Date.now() - startTime,
      };
    }
  }

  /**
   * Check memory usage
   */
  private checkMemoryUsage(): HealthCheckResult {
    const memUsage = process.memoryUsage();
    const heapUsedMB = Math.round(memUsage.heapUsed / 1024 / 1024);
    const rssUsedMB = Math.round(memUsage.rss / 1024 / 1024);

    let status: 'healthy' | 'unhealthy' | 'warning' = 'healthy';
    let message = `Memory usage: ${heapUsedMB}MB heap, ${rssUsedMB}MB RSS`;

    if (rssUsedMB > this.config.memoryThreshold) {
      status = 'unhealthy';
      message = `High memory usage: ${rssUsedMB}MB (threshold: ${this.config.memoryThreshold}MB)`;
    } else if (rssUsedMB > this.config.memoryThreshold * 0.8) {
      status = 'warning';
      message = `Memory usage approaching threshold: ${rssUsedMB}MB`;
    }

    return {
      name: 'memory',
      status,
      message,
      timestamp: Date.now(),
    };
  }

  /**
   * Check process uptime
   */
  private checkProcessUptime(): HealthCheckResult {
    const uptimeSeconds = process.uptime();
    const uptimeMinutes = Math.round(uptimeSeconds / 60);

    return {
      name: 'uptime',
      status: 'healthy',
      message: `Process uptime: ${uptimeMinutes} minutes`,
      timestamp: Date.now(),
    };
  }

  /**
   * Check event loop lag
   */
  private async checkEventLoopLag(): Promise<HealthCheckResult> {
    return new Promise(resolve => {
      const start = process.hrtime.bigint();

      setImmediate(() => {
        const lag = Number(process.hrtime.bigint() - start) / 1000000; // Convert to milliseconds

        let status: 'healthy' | 'unhealthy' | 'warning' = 'healthy';
        let message = `Event loop lag: ${lag.toFixed(2)}ms`;

        if (lag > 100) {
          status = 'unhealthy';
          message = `High event loop lag: ${lag.toFixed(2)}ms`;
        } else if (lag > 50) {
          status = 'warning';
          message = `Elevated event loop lag: ${lag.toFixed(2)}ms`;
        }

        resolve({
          name: 'eventloop',
          status,
          message,
          timestamp: Date.now(),
          responseTime: lag,
        });
      });
    });
  }

  /**
   * Check WordPress connectivity
   */
  private async checkWordPress(): Promise<HealthCheckResult> {
    const startTime = Date.now();
    const requireWpOnStartup = process.env.REQUIRE_WP_ON_STARTUP !== 'false';

    try {
      const { wpAuthService } = await import('../../features/wordpress/services/wp-auth');
      await wpAuthService.authenticate();
      const responseTime = Date.now() - startTime;

      return {
        name: 'wordpress',
        status: responseTime > 10000 ? 'warning' : 'healthy',
        message:
          responseTime > 10000
            ? `Slow WordPress response: ${responseTime}ms`
            : 'WordPress connection OK',
        timestamp: Date.now(),
        responseTime,
      };
    } catch (error) {
      // In graceful degradation mode, WordPress failures are warnings, not unhealthy
      const status = requireWpOnStartup ? 'unhealthy' : 'warning';
      const message = requireWpOnStartup
        ? `WordPress connection failed: ${error}`
        : `WordPress unavailable (graceful degradation mode): ${error}`;

      return {
        name: 'wordpress',
        status,
        message,
        timestamp: Date.now(),
        responseTime: Date.now() - startTime,
      };
    }
  }

  /**
   * Get current health status
   */
  getStatus() {
    return {
      isRunning: this.isRunning,
      lastHealthCheck: this.lastHealthCheck,
      consecutiveFailures: this.consecutiveFailures,
      config: this.config,
    };
  }
}

// Create singleton instance
const healthMonitor = new HealthMonitor();

export { HealthMonitor, healthMonitor };
