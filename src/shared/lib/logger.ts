import winston from 'winston';
import 'winston-daily-rotate-file';
import fs from 'fs';
import path from 'path';

// Ensure logs directory exists
const logsDir = path.join(process.cwd(), 'logs');
if (!fs.existsSync(logsDir)) {
  fs.mkdirSync(logsDir);
}

// Configure rotating file transports
const errorRotateFile = new winston.transports.DailyRotateFile({
  filename: 'logs/error-%DATE%.log',
  datePattern: 'YYYY-MM-DD',
  level: 'error',
  maxSize: '20m',
  maxFiles: '14d',
  zippedArchive: true,
});

const combinedRotateFile = new winston.transports.DailyRotateFile({
  filename: 'logs/combined-%DATE%.log',
  datePattern: 'YYYY-MM-DD',
  maxSize: '20m',
  maxFiles: '14d',
  zippedArchive: true,
});

// Configure transports array - FILE ONLY to prevent EPIPE errors
const transports: winston.transport[] = [
  errorRotateFile,
  combinedRotateFile,
];

// NEVER add console transport in production to prevent EPIPE errors
// Console transport can cause crashes when stdout/stderr are closed or redirected
const isProduction = process.env.NODE_ENV === 'production';
const enableConsoleLogging = process.env.ENABLE_CONSOLE_LOGGING === 'true';

// Only allow console logging in development AND when explicitly enabled
// This prevents accidental console logging in production environments
if (!isProduction && enableConsoleLogging) {
  transports.push(
    new winston.transports.Console({
      format: winston.format.combine(winston.format.colorize(), winston.format.simple()),
      handleExceptions: false, // Don't handle exceptions to avoid EPIPE issues
      handleRejections: false, // Don't handle rejections to avoid EPIPE issues
    })
  );
}

// Configure logger
const logger = winston.createLogger({
  level: process.env.LOG_LEVEL || 'info',
  format: winston.format.combine(winston.format.timestamp(), winston.format.json()),
  transports,
  // Disable exception handling by winston to prevent EPIPE issues
  handleExceptions: false,
  handleRejections: false,
  // Add error handling for transports
  exceptionHandlers: [
    errorRotateFile, // Only log exceptions to file, not console
  ],
  rejectionHandlers: [
    errorRotateFile, // Only log rejections to file, not console
  ],
});

// Add error handling for transport errors - NO CONSOLE OUTPUT to prevent EPIPE
errorRotateFile.on('error', (error) => {
  // Write directly to a fallback file instead of console to prevent EPIPE
  try {
    const fs = require('fs');
    const fallbackLog = `${new Date().toISOString()} - Error in error log transport: ${error.message}\n`;
    fs.appendFileSync('logs/transport-errors.log', fallbackLog);
  } catch {
    // If even file writing fails, silently continue to prevent crashes
  }
});

combinedRotateFile.on('error', (error) => {
  // Write directly to a fallback file instead of console to prevent EPIPE
  try {
    const fs = require('fs');
    const fallbackLog = `${new Date().toISOString()} - Error in combined log transport: ${error.message}\n`;
    fs.appendFileSync('logs/transport-errors.log', fallbackLog);
  } catch {
    // If even file writing fails, silently continue to prevent crashes
  }
});

// Create a bulletproof logger wrapper that handles ALL errors including EPIPE
const createSafeLogMethod = (level: string) => {
  return (message: any, meta?: any) => {
    try {
      (logger as any)[level](message, meta);
    } catch (error: any) {
      if (error.code === 'EPIPE' || error.code === 'ENOTCONN' || error.code === 'ECONNRESET') {
        // Silently ignore pipe/connection errors to prevent crashes
        return;
      }

      // For other errors, try to write to a fallback file
      try {
        const fs = require('fs');
        const fallbackLog = `${new Date().toISOString()} - [${level.toUpperCase()}] Logger Error: ${error.message} | Original: ${message}\n`;
        fs.appendFileSync('logs/logger-errors.log', fallbackLog);
      } catch {
        // If even fallback fails, silently continue to prevent crashes
      }
    }
  };
};

const safeLogger = {
  error: createSafeLogMethod('error'),
  warn: createSafeLogMethod('warn'),
  info: createSafeLogMethod('info'),
  debug: createSafeLogMethod('debug'),
  verbose: createSafeLogMethod('verbose'),
  silly: createSafeLogMethod('silly'),

  // Additional safe methods for common logging patterns
  log: createSafeLogMethod('info'), // Alias for info

  // Emergency logging method that bypasses Winston entirely
  emergency: (message: any, meta?: any) => {
    try {
      const fs = require('fs');
      const timestamp = new Date().toISOString();
      const logEntry = `${timestamp} - [EMERGENCY] ${typeof message === 'string' ? message : JSON.stringify(message)}${meta ? ' | Meta: ' + JSON.stringify(meta) : ''}\n`;
      fs.appendFileSync('logs/emergency.log', logEntry);
    } catch {
      // If even emergency logging fails, there's nothing more we can do
    }
  },

  // Expose the original logger for cases where you need it (use with caution)
  _original: logger,
};

export { safeLogger as logger };
