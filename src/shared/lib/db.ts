import mysql, { Pool, PoolConnection, ResultSetHeader, RowDataPacket } from 'mysql2/promise';
import { logger } from './logger';

// Database connection pool configuration
interface PoolConfig {
  host: string;
  port: number;
  user: string;
  password: string;
  database: string;
  waitForConnections: boolean;
  connectionLimit: number;
  queueLimit: number;
  enableKeepAlive: boolean;
  keepAliveInitialDelay: number;
  namedPlaceholders: boolean;
  connectTimeout: number;
  maxIdle: number;
  idleTimeout: number;
}

const poolConfig: PoolConfig = {
  host: process.env.DB_HOST || 'localhost',
  port: parseInt(process.env.DB_PORT || '3306', 10),
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || '',
  database: process.env.DB_NAME || 'myapp',
  waitForConnections: true,
  connectionLimit: parseInt(process.env.DB_CONNECTION_LIMIT || '20', 10),
  queueLimit: 0,
  enableKeepAlive: true,
  keepAliveInitialDelay: 10000,
  namedPlaceholders: true,
  connectTimeout: 10000,
  maxIdle: parseInt(process.env.DB_CONNECTION_LIMIT || '20', 10) / 2,
  idleTimeout: parseInt(process.env.DB_IDLE_TIMEOUT || '60000', 10),
};

const pool: Pool = mysql.createPool(poolConfig);

// Add a request counter and throttling mechanism
const requestCounts = new Map<string, { count: number; timestamp: number }>();

// Database connection utilities
interface DbConnect {
  getConnection(): Promise<PoolConnection>;
  testConnection(): Promise<boolean>;
  testConnectionWithRetry(maxRetries?: number, retryDelay?: number): Promise<boolean>;
  query<T extends RowDataPacket[] | RowDataPacket[][] | ResultSetHeader>(
    sql: string,
    params?: any,
    timeout?: number
  ): Promise<T>;
  transaction<T>(callback: (connection: PoolConnection) => Promise<T>): Promise<T>;
  throttleByIp(ip: string, limit: number, windowMs: number): boolean;
  close(): Promise<void>;
}

const dbConnect: DbConnect = {
  // Get connection from pool
  getConnection: async (): Promise<PoolConnection> => {
    try {
      return await pool.getConnection();
    } catch (error) {
      logger.error('Error getting DB connection:', error);
      throw error;
    }
  },

  // Test connection
  testConnection: async (): Promise<boolean> => {
    let conn: PoolConnection | undefined;
    try {
      conn = await pool.getConnection();
      await conn.query('SELECT 1');
      return true;
    } catch (error) {
      logger.error('Database connection test failed:', error);
      throw error;
    } finally {
      if (conn) conn.release();
    }
  },

  // Test connection with retry logic
  testConnectionWithRetry: async (maxRetries: number = 5, retryDelay: number = 2000): Promise<boolean> => {
    let lastError: any;

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        logger.info(`Database connection attempt ${attempt}/${maxRetries}`);
        await dbConnect.testConnection();
        logger.info('Database connection successful');
        return true;
      } catch (error) {
        lastError = error;
        const errorCode = (error as any)?.code;
        const isTimeoutError = errorCode === 'ETIMEDOUT' || errorCode === 'ECONNREFUSED' || errorCode === 'ENOTFOUND';

        if (attempt === maxRetries) {
          logger.error(`Database connection failed after ${maxRetries} attempts:`, error);
          throw error;
        }

        if (isTimeoutError) {
          const delay = retryDelay * Math.pow(1.5, attempt - 1); // Exponential backoff
          logger.warn(`Database connection attempt ${attempt} failed (${errorCode}), retrying in ${delay}ms...`);
          await new Promise(resolve => setTimeout(resolve, delay));
        } else {
          // For non-timeout errors, don't retry
          logger.error('Database connection failed with non-retryable error:', error);
          throw error;
        }
      }
    }

    throw lastError;
  },

  // Execute query with pooled connection
  query: async <T extends RowDataPacket[] | RowDataPacket[][] | ResultSetHeader>(
    sql: string,
    params?: any,
    timeout: number = 10000 // 10 second default timeout
  ): Promise<T> => {
    try {
      console.log('SQL Query:', sql);
      console.log('Parameters:', params);

      const [results] = await pool.query<T>({
        sql,
        values: params,
        timeout, // Add query timeout
      });
      return results;
    } catch (error) {
      logger.error('Query error:', error);
      throw error;
    }
  },

  // Execute transaction
  transaction: async <T>(callback: (connection: PoolConnection) => Promise<T>): Promise<T> => {
    let conn: PoolConnection | undefined;
    try {
      conn = await pool.getConnection();
      await conn.beginTransaction();

      // Add query logging to the connection
      const originalQuery = conn.query;
      conn.query = function (...args: any[]): any {
        console.log('Transaction SQL:', args[0]);
        console.log('Transaction params:', args[1]);
        return originalQuery.apply(conn, args as Parameters<typeof originalQuery>);
      };

      const result = await callback(conn);

      await conn.commit();
      return result;
    } catch (error) {
      if (conn) await conn.rollback();
      logger.error('Transaction error:', error);
      throw error;
    } finally {
      if (conn) conn.release();
    }
  },

  // Throttle requests by IP
  throttleByIp: (ip: string, limit: number, windowMs: number): boolean => {
    const now = Date.now();
    const record = requestCounts.get(ip) || { count: 0, timestamp: now };

    // Reset counter if window expired
    if (now - record.timestamp > windowMs) {
      record.count = 1;
      record.timestamp = now;
      requestCounts.set(ip, record);
      return false;
    }

    // Check if over limit
    if (record.count >= limit) {
      return true;
    }

    // Increment counter
    record.count++;
    requestCounts.set(ip, record);
    return false;
  },

  // Close the connection pool
  close: async (): Promise<void> => {
    try {
      logger.info('Closing database connection pool...');
      await pool.end();
      logger.info('Database connection pool closed successfully');
    } catch (error) {
      logger.error('Error closing database connection pool:', error);
      throw error;
    }
  },
};

// Monitor pool statistics
setInterval(() => {
  try {
    const stats = {
      threadId: pool.threadId,
      connectionLimit: poolConfig.connectionLimit,
      // Safely access pool properties
      queueSize: (pool as any)._acquiringConnections?.length || 0,
      freeConnections: (pool as any)._freeConnections?.length || 0,
      totalConnections: (pool as any)._allConnections?.length || 0,
    };

    logger.debug('DB Pool Status:', stats);
  } catch (error) {
    logger.error('Error monitoring pool stats:', error);
  }
}, 60000); // Log every minute

export { dbConnect, pool };
