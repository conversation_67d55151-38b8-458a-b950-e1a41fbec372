import { Request, Response, NextFunction } from 'express';
import { logger } from '../lib/logger';

// Store request counts by IP
const ipRequestCounts = new Map<string, { count: number; timestamp: number }>();

export const dbRateLimiter = (
  limit: number = 100,
  windowMs: number = 60000,
  enabled: boolean = true
) => {
  return (req: Request, res: Response, next: NextFunction) => {
    // Skip rate limiting if disabled
    if (!enabled) {
      return next();
    }

    const ip = req.ip || req.socket.remoteAddress || 'unknown';
    const now = Date.now();
    const record = ipRequestCounts.get(ip) || { count: 0, timestamp: now };

    // Reset if window expired
    if (now - record.timestamp > windowMs) {
      record.count = 1;
      record.timestamp = now;
      ipRequestCounts.set(ip, record);
      return next();
    }

    // Check if over limit
    if (record.count >= limit) {
      logger.warn(`Rate limit exceeded for IP: ${ip}`);
      return res.status(429).json({
        message: 'Too many requests, please try again later',
      });
    }

    // Increment counter
    record.count++;
    ipRequestCounts.set(ip, record);
    next();
  };
};
