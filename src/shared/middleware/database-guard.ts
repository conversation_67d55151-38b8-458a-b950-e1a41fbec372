import { Request, Response, NextFunction } from 'express';
import { logger } from '../lib/logger';

/**
 * Middleware to check database connection state before processing requests
 * that require database access
 */
export const requireDatabase = (req: Request, res: Response, next: NextFunction): void => {
  // Import getDatabaseConnectionState dynamically to avoid circular imports
  import('../../index').then(({ getDatabaseConnectionState }) => {
    if (!getDatabaseConnectionState()) {
      logger.warn(`Database required but unavailable for ${req.method} ${req.path}`);
      
      res.status(503).json({
        status: 'error',
        message: 'Service temporarily unavailable - database connection required',
        code: 'DATABASE_UNAVAILABLE',
        timestamp: new Date().toISOString()
      });
      return;
    }
    
    next();
  }).catch((error) => {
    logger.error('Error checking database state:', error);
    res.status(500).json({
      status: 'error',
      message: 'Internal server error',
      timestamp: new Date().toISOString()
    });
  });
};

/**
 * Middleware that allows requests to proceed but adds database status to response
 * Useful for endpoints that can work with cached data or provide limited functionality
 */
export const databaseAware = (req: Request, res: Response, next: NextFunction): void => {
  import('../../index').then(({ getDatabaseConnectionState }) => {
    // Add database status to request object for use in route handlers
    (req as any).databaseAvailable = getDatabaseConnectionState();
    next();
  }).catch((error) => {
    logger.error('Error checking database state:', error);
    (req as any).databaseAvailable = false;
    next();
  });
};

/**
 * Helper function to create database-aware responses
 */
export const createDatabaseAwareResponse = (data: any, databaseAvailable: boolean) => {
  return {
    ...data,
    meta: {
      databaseAvailable,
      timestamp: new Date().toISOString(),
      ...(data.meta || {})
    }
  };
};
