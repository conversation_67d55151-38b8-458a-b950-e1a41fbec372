import { UtcDateTimeIso } from '../types/common';

// Regardless of what gets returned:
// 2025-06-24T17:30:00.000Z"
// 2025-06-24T17:30:00+01:00"
// it is ALWAYS local time, e.g. 17:30
// strip everything off and return: 2025-06-24T17:30

// schedule.eventStartDate = schedule.eventStartDate.toString().slice(0, 19);

/**
 * Formats a date string or Date object to a consistent ISO format (YYYY-MM-DDTHH:MM)
 * Handles both string dates and Date objects
 * @param date The date to format (string or Date object)
 * @returns Formatted date string in YYYY-MM-DDTHH:MM format
 */
export const forceToLocalDateTime = (date: string | Date | null | undefined): string => {
  if (!date) return '';

  if (typeof date === 'string') {
    return date.slice(0, 19);
  } else {
    // return local time
    return date.toISOString().slice(0, 19);
  }
};
