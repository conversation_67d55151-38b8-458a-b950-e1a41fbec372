import express from 'express';
import { competitionRouter } from '../features/competitions/api';
import { resultRouter } from '../features/competition-results/api';
import { adminRouter } from '../features/admin/api';
import { authRouter } from '../features/auth/api';
import { scraperRouter } from '../features/web-scraper';
import { wordpressRouter } from '../features/wordpress';

// Create a new router instance
const apiRouter = express.Router();

// Add debugging middleware
apiRouter.use((req, res, next) => {
  console.log(`src/api/index.ts API ROUTER: ${req.method} ${req.originalUrl} (${req.url})`);
  next();
});

// Add a test route to verify the router is working
apiRouter.get('/test', (req, res) => {
  console.log('src/api/index.ts API test route hit');
  res.json({ message: 'src/api/index.ts API router test route is working!' });
});

// Mount each feature's router
apiRouter.use('/competitions', competitionRouter);
apiRouter.use('/results', resultRouter);
apiRouter.use('/admin', adminRouter);
apiRouter.use('/auth', authRouter);
apiRouter.use('/scraper', scraperRouter);
apiRouter.use('/wordpress', wordpressRouter);

// Add a catch-all route for debugging
apiRouter.use('/', (req, res) => {
  console.log(`src/api/index.ts No API route matched: ${req.method} ${req.originalUrl}`);
  res.status(404).json({ message: 'src/api/index.ts API route not found' });
});

// Make sure all routes are properly defined
// No routes should have patterns like /:/ or /:/something
// All parameters should have names, like /:id or /:userId

// Export the router
export { apiRouter };
