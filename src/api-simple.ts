import express from 'express';

// Create a new router instance
const apiRouter = express.Router();

// Add debugging middleware
apiRouter.use((req, res, next) => {
  console.log(`SIMPLE API ROUTER: ${req.method} ${req.originalUrl} (${req.url})`);
  next();
});

// Add a test route to verify the router is working
apiRouter.get('/test', (req, res) => {
  console.log('Simple API test route hit');
  res.json({ message: 'Simple API router test route is working!' });
});

// Export the router
export { apiRouter };