import express from 'express';

// Create a new Express application
const app = express();

// Add logging middleware
app.use((req, res, next) => {
  console.log(`Simplified app: ${req.method} ${req.url}`);
  next();
});

// Add a simple API router
const apiRouter = express.Router();

apiRouter.use((req, res, next) => {
  console.log(`Simplified API router: ${req.method} ${req.url}`);
  next();
});

// Add a test route to the API router
apiRouter.get('/test', (req, res) => {
  res.json({ message: 'API test route is working!' });
});

// Add a competitions route
apiRouter.get('/competitions/:id', (req, res) => {
  res.json({ message: 'Competition route is working!', id: req.params.id });
});

// Add an entries route
apiRouter.get('/entries/competition/:competitionId', (req, res) => {
  res.json({
    message: 'Entries by competition route is working!',
    competitionId: req.params.competitionId,
  });
});

// Mount the API router
app.use('/api', apiRouter);

// Add a root route
app.get('/', (req, res) => {
  res.send('Simplified app is working!');
});

// Start the server
const PORT = 3002;
app.listen(PORT, () => {
  console.log(`Simplified app running on port ${PORT}`);
});
