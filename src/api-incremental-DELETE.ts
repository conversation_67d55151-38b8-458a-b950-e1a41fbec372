import express from 'express';
import { competitionRouter } from './features/competitions/api';
import { entryRouter } from './features/competition-entries/api';
import { scheduleRouter } from './features/competition-schedules/api';
import { teamRouter } from './features/competition-teams/api';
import { resultRouter } from './features/competition-results/api';

// Create a new router instance
const apiRouter = express.Router();

/*
// Add debugging middleware
apiRouter.use((req, res, next) => {
  console.log(`api-incremental.ts API ROUTER: ${req.method} ${req.originalUrl} (${req.url})`);
  next();
});

// Add a test route to verify the router is working
apiRouter.get('/test', (req, res) => {
  console.log('api-incremental.ts API test route hit');
  res.json({ message: 'api-incremental.ts API router test route is working!' });
});

// Mount each feature's router
// console.log('api-incremental.ts Mounting competitions router at /competitions');
apiRouter.use('/competitions', competitionRouter);

console.log('Mounting entries router at /entries');
apiRouter.use('/entries', entryRouter);

console.log('api-incremental.ts Mounting schedules router at /schedules');
apiRouter.use('/schedules', scheduleRouter);

console.log('api-incremental.ts Mounting teams router at /teams');
apiRouter.use('/teams', teamRouter);

console.log('api-incremental.ts Mounting results router at /results');
apiRouter.use('/results', resultRouter);

// Add a catch-all route for debugging
apiRouter.use('/', (req, res) => {
  console.log(`api-incremental.ts No API route matched: ${req.method} ${req.originalUrl}`);
  res.status(404).json({ message: 'api-incremental.ts API route not found' });
});
*/

// Export the router
export { apiRouter };
