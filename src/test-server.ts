import express from 'express';
import { testApiRouter } from './api-test';

const app = express();

// Root level logging
app.use((req, res, next) => {
  console.log(`Test server: ${req.method} ${req.url}`);
  next();
});

// Mount the test API router
console.log('Mounting test API router at /api');
app.use('/api', testApiRouter);

// Add a root route
app.get('/', (req, res) => {
  console.log('Root route hit');
  res.send('Test server is working!');
});

// Start the server
const PORT = 3003;
app.listen(PORT, () => {
  console.log(`Test server running on port ${PORT}`);
  console.log(`Test the server at: http://localhost:${PORT}/`);
  console.log(`Test the API at: http://localhost:${PORT}/api/test`);
  console.log(`Test the competitions route at: http://localhost:${PORT}/api/competitions/123`);
});
