import { dbConnect } from '../../../shared/lib/db';
import { CompetitionAthlete } from '../models/types';

const competitionAthleteService = {
  getByCompetitionId: async (competitionId: number): Promise<CompetitionAthlete[]> => {
    const sql = `SELECT * FROM Entry4_AthleteView WHERE compid = ?`;
    // const sql = `SELECT * FROM Entry4_AthleteView LIMIT 35`;

    return await dbConnect.query<CompetitionAthlete[]>(sql, [competitionId]);
  },

  getById: async (id: number): Promise<CompetitionAthlete | undefined> => {
    const sql = `
      SELECT * FROM Entry4_AthleteView WHERE id = ?
    `;

    const athletes = await dbConnect.query<CompetitionAthlete[]>(sql, [id]);
    return athletes[0];
  },

  getByClub: async (competitionId: number, clubId: number): Promise<CompetitionAthlete[]> => {
    const sql = `
      SELECT * FROM Entry4_AthleteView 
      WHERE compid = ? AND clubid = ?
    `;

    return await dbConnect.query<CompetitionAthlete[]>(sql, [competitionId, clubId]);
  },
};

export { competitionAthleteService };
