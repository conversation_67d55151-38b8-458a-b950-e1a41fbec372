import express from 'express';
import { getAthletesByCompetition, getAthleteById, getAthletesByClub } from '../controllers';

const athleteRouter = express.Router();

// Add debugging middleware
athleteRouter.use((req, res, next) => {
  console.log(`ATHLETES ROUTER: ${req.method} ${req.originalUrl} (${req.url})`);
  next();
});

// Routes for athletes (read-only)
athleteRouter.get('/competition/:competitionId', getAthletesByCompetition);
athleteRouter.get('/competition/:competitionId/club/:clubId', getAthletesByClub);
athleteRouter.get('/:id', getAthleteById);

// Add a test route to verify the router is working
athleteRouter.get('/test', (req, res) => {
  res.json({ message: 'Athletes router test route is working!' });
});

// Export the router
export { athleteRouter };