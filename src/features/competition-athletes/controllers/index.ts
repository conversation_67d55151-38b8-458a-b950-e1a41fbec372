import { Request, Response, NextFunction } from 'express';
import { competitionAthleteService } from '../services';
import { logger } from '../../../shared/lib/logger';

export const getAthletesByCompetition = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const competitionId = parseInt(req.params.competitionId, 10);
    
    if (isNaN(competitionId)) {
      res.status(400).json({ message: 'Invalid competition ID' });
      return;
    }
    
    const athletes = await competitionAthleteService.getByCompetitionId(competitionId);
    res.json({ data: athletes });
  } catch (error) {
    next(error);
  }
};

export const getAthleteById = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const id = parseInt(req.params.id, 10);
    
    if (isNaN(id)) {
      res.status(400).json({ message: 'Invalid athlete ID' });
      return;
    }
    
    const athlete = await competitionAthleteService.getById(id);
    
    if (!athlete) {
      res.status(404).json({ message: 'Athlete not found' });
      return;
    }
    
    res.json({ data: athlete });
  } catch (error) {
    next(error);
  }
};

export const getAthletesByClub = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const competitionId = parseInt(req.params.competitionId, 10);
    const clubId = parseInt(req.params.clubId, 10);
    
    if (isNaN(competitionId) || isNaN(clubId)) {
      res.status(400).json({ message: 'Invalid competition or club ID' });
      return;
    }
    
    const athletes = await competitionAthleteService.getByClub(competitionId, clubId);
    res.json({ data: athletes });
  } catch (error) {
    next(error);
  }
};