// Public API for the competition-athletes feature
import { athleteRouter } from './api';
import { 
  getAthletesByCompetition,
  getAthleteById,
  getAthletesByClub
} from './controllers';
import { competitionAthleteService } from './services';
import { CompetitionAthlete } from './models/types';

export {
  athleteRouter,
  getAthletesByCompetition,
  getAthleteById,
  getAthletesByClub,
  competitionAthleteService,
  CompetitionAthlete
};