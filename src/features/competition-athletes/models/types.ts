import { RowDataPacket } from 'mysql2/promise';

export interface CompetitionAthlete extends RowDataPacket {
  id: number;
  compId: number;
  firstName: string;
  surName: string;
  dob: Date;
  urn: string;
  // clubId?: number;
  // clubName?: string;
  // These fields can be updated once we know the exact schema
  // Add other fields based on your SQL query results
}

/*
export interface CompetitionAthlete extends RowDataPacket {
  id: number;
  competitionId: number;
  athleteId: number;
  athleteName: string;
  clubId?: number;
  clubName?: string;
  // These fields can be updated once we know the exact schema
  // Add other fields based on your SQL query results
}
*/
