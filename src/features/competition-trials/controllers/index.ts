import { Request, Response, NextFunction } from 'express';
import { competitionTrialService } from '../services';
import { logger } from '../../../shared/lib/logger';

export const getTrialsByCompetition = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const competitionId = parseInt(req.params.competitionId, 10);
    
    if (isNaN(competitionId)) {
      res.status(400).json({ message: 'Invalid competition ID' });
      return;
    }
    
    const trials = await competitionTrialService.getByCompetitionId(competitionId);
    res.json({ data: trials });
  } catch (error) {
    next(error);
  }
};

export const getTrialsByAthlete = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const competitionId = parseInt(req.params.competitionId, 10);
    const athleteId = parseInt(req.params.athleteId, 10);
    
    if (isNaN(competitionId) || isNaN(athleteId)) {
      res.status(400).json({ message: 'Invalid competition or athlete ID' });
      return;
    }
    
    const trials = await competitionTrialService.getByAthlete(competitionId, athleteId);
    res.json({ data: trials });
  } catch (error) {
    next(error);
  }
};

export const getTrialsByEvent = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const competitionId = parseInt(req.params.competitionId, 10);
    const eventId = parseInt(req.params.eventId, 10);
    
    if (isNaN(competitionId) || isNaN(eventId)) {
      res.status(400).json({ message: 'Invalid competition or event ID' });
      return;
    }
    
    const trials = await competitionTrialService.getByEvent(competitionId, eventId);
    res.json({ data: trials });
  } catch (error) {
    next(error);
  }
};