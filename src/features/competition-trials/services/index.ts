import { dbConnect } from '../../../shared/lib/db';
import { CompetitionTrial } from '../models/types';

const competitionTrialService = {
  getByCompetitionId: async (competitionId: number): Promise<CompetitionTrial[]> => {
    const sql = `SELECT * FROM Entry4_uk_TrialResultsView WHERE compid = ?`;

    return await dbConnect.query<CompetitionTrial[]>(sql, [competitionId]);
  },

  getByAthlete: async (competitionId: number, athleteId: number): Promise<CompetitionTrial[]> => {
    const sql = `
      SELECT * FROM Entry4_uk_TrialResultsView 
      WHERE compid = ? AND athleteId = ?
    `;

    return await dbConnect.query<CompetitionTrial[]>(sql, [competitionId, athleteId]);
  },

  getByEvent: async (competitionId: number, eventId: number): Promise<CompetitionTrial[]> => {
    const sql = `
      SELECT * FROM Entry4_uk_TrialResultsView 
      WHERE compid = ? AND eventId = ?
    `;

    return await dbConnect.query<CompetitionTrial[]>(sql, [competitionId, eventId]);
  },
};

export { competitionTrialService };
