import express from 'express';
import { getTrialsByCompetition, getTrialsByAthlete, getTrialsByEvent } from '../controllers';

const trialRouter = express.Router();

// Add debugging middleware
trialRouter.use((req, res, next) => {
  console.log(`TRIALS ROUTER: ${req.method} ${req.originalUrl} (${req.url})`);
  next();
});

// Routes for trials (read-only)
trialRouter.get('/competition/:competitionId', getTrialsByCompetition);
trialRouter.get('/competition/:competitionId/athlete/:athleteId', getTrialsByAthlete);
trialRouter.get('/competition/:competitionId/event/:eventId', getTrialsByEvent);

// Add a test route to verify the router is working
trialRouter.get('/test', (req, res) => {
  res.json({ message: 'Trials router test route is working!' });
});

// Export the router
export { trialRouter };