import { RowDataPacket } from 'mysql2/promise';
import { StringifiedJSON } from '../../../shared/types/common';

/**
 * {
 *   "compId": 695,
 *   "egId": 16070,
 *   "athleteId": 198072,
 *   "resultKey": "1.50:1",
 *   "resultValue": "O",
 *   "options": null
 * }
 */

export type TrialOptions = unknown;

export interface CompetitionTrial extends RowDataPacket {
  compId: number;
  egId: number;
  athleteId: number;
  resultKey: string;
  resultValue: string;
  options: null | StringifiedJSON<TrialOptions>;
}
