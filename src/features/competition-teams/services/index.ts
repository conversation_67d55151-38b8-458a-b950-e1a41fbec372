import { dbConnect } from '../../../shared/lib/db';
import { CompetitionTeam, TeamMember } from '../models/types';

const competitionTeamService = {
  getByCompetitionId: async (competitionId: number): Promise<CompetitionTeam[]> => {
    const sql = `Select * from Entry4_uk_TeamEntriesView where compid = ?`;

    return await dbConnect.query<CompetitionTeam[]>(sql, [competitionId]);
  },

  getById: async (id: number): Promise<CompetitionTeam | undefined> => {
    const sql = `
      SELECT 
        t.id,
        t.competition_id as competitionId,
        t.team_name as teamName,
        t.club_id as clubId,
        c.name as clubName,
        t.category_id as categoryId,
        cat.name as categoryName,
        t.entry_date as entryDate,
        t.status,
        t.payment_status as paymentStatus,
        COUNT(tm.id) as memberCount
      FROM 
        competition_teams t
      JOIN 
        clubs c ON t.club_id = c.id
      JOIN 
        categories cat ON t.category_id = cat.id
      LEFT JOIN 
        team_members tm ON t.id = tm.team_id
      WHERE 
        t.id = ?
      GROUP BY 
        t.id
    `;

    const teams = await dbConnect.query<CompetitionTeam[]>(sql, [id]);
    return teams[0];
  },

  getTeamMembers: async (teamId: number): Promise<TeamMember[]> => {
    const sql = `
      SELECT 
        tm.id,
        tm.team_id as teamId,
        tm.athlete_id as athleteId,
        a.name as athleteName,
        tm.role
      FROM 
        team_members tm
      JOIN 
        athletes a ON tm.athlete_id = a.id
      WHERE 
        tm.team_id = ?
      ORDER BY 
        tm.role, a.name
    `;

    return await dbConnect.query<TeamMember[]>(sql, [teamId]);
  },
};

export { competitionTeamService };
