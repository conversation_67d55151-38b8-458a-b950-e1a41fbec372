import express from 'express';
import { getTeamsByCompetition, getTeamById, getTeamMembers } from '../controllers';

const teamRouter = express.Router();

// Add debugging middleware
teamRouter.use((req, res, next) => {
  console.log(`TEAMS ROUTER: ${req.method} ${req.originalUrl} (${req.url})`);
  next();
});

// Routes for teams (read-only)
teamRouter.get('/competition/:competitionId', getTeamsByCompetition);
teamRouter.get('/:id', getTeamById);
teamRouter.get('/:teamId/members', getTeamMembers);

// Add a test route to verify the router is working
teamRouter.get('/test', (req, res) => {
  res.json({ message: 'Teams router test route is working!' });
});

// Export the router
export { teamRouter };
