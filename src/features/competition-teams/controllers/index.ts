import { Request, Response, NextFunction } from 'express';
import { competitionTeamService } from '../services';
import { logger } from '../../../shared/lib/logger';

export const getTeamsByCompetition = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const competitionId = parseInt(req.params.competitionId, 10);
    
    if (isNaN(competitionId)) {
      res.status(400).json({ message: 'Invalid competition ID' });
      return;
    }
    
    const teams = await competitionTeamService.getByCompetitionId(competitionId);
    res.json({ data: teams });
  } catch (error) {
    next(error);
  }
};

export const getTeamById = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const id = parseInt(req.params.id, 10);
    
    if (isNaN(id)) {
      res.status(400).json({ message: 'Invalid team ID' });
      return;
    }
    
    const team = await competitionTeamService.getById(id);
    
    if (!team) {
      res.status(404).json({ message: 'Team not found' });
      return;
    }
    
    res.json({ data: team });
  } catch (error) {
    next(error);
  }
};

export const getTeamMembers = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const teamId = parseInt(req.params.teamId, 10);
    
    if (isNaN(teamId)) {
      res.status(400).json({ message: 'Invalid team ID' });
      return;
    }
    
    const members = await competitionTeamService.getTeamMembers(teamId);
    res.json({ data: members });
  } catch (error) {
    next(error);
  }
};