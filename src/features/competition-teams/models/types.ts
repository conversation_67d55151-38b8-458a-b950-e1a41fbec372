import { RowDataPacket } from 'mysql2/promise';

export interface CompetitionTeam extends RowDataPacket {
  entryId: number;
  compId: number;
  ceId: number;
  egId: number;
  eventName: string;
  athleteId: number;
  teamBibNo: string;
  teamName: string;
  present: number;
  heatno: number;
  laneno: number;
  heatnocheckedin: number;
  lanenocheckedin: number;
}

export interface TeamMember extends RowDataPacket {
  id: number;
  teamId: number;
  athleteId: number;
  athleteName: string;
  role: string;
  // Add other fields based on your SQL query results
}
