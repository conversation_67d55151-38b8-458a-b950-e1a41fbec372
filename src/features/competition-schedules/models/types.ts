import { RowDataPacket } from 'mysql2/promise';
import { StringifiedJSON, TypeNo, UtcDateTimeIso } from '../../../shared/types/common';

/*
{
				"egId": 13729,
				"compId": 617,
				"eventNo": 5,
				"eventName": "100m JB",
				"eventStartDate": "2024-07-12T09:28:00.000Z",
				"typeNo": "T5",
				"egOptions": "{\"maxInHeat\":9,\"seed\":{\"laneCount\":9,\"type\":\"H\",\"seeded\":true,\"doubleup\":\"\",\"qualifyToEg\":{\"id\":13730,\"compId\":617,\"name\":\"100m JB Final\",\"eventNo\":52,\"rules\":{\"auto\":1,\"nonAuto\":4}}},\"checkIn\":{\"from\":-1,\"to\":-1,\"seedOnEntries\":false,\"checkInMins\":60}}",
				"ceId": 70685,
				"ageGroupId": 213,
				"ageGroup": "ES Juniors",
				"ageGroupKey": "ES Juniors"
			}
*/
export interface CompetitionSchedule extends RowDataPacket {
  egId: number;
  compId: number;
  eventNo: number;
  eventName: string;
  eventStartDate: UtcDateTimeIso;
  typeNo: TypeNo;
  egOptions: StringifiedJSON<EventGroupOptions>;
  ceId: number;
  ageGroupId: number;
  ageGroup: string;
  ageGroupKey: string;
}

export type EventGroupOptions = {
  maxInHeat: number;
  seed: {
    laneCount: number;
    type: string;
    seeded: boolean;
    doubleup: string;
    qualifyToEg: {
      id: number;
      compId: number;
      name: string;
      eventNo: number;
      rules: {
        auto: number;
        nonAuto: number;
      };
    };
  };
  checkIn: {
    from: number;
    to: number;
    seedOnEntries: boolean;
    checkInMins: number;
  };
  // Add other fields based on your SQL query results
};
