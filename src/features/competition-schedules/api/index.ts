import express from 'express';
import { getSchedulesByCompetition, getScheduleById } from '../controllers';

const scheduleRouter = express.Router();

// Add debugging middleware
scheduleRouter.use((req, res, next) => {
  console.log(`SCHEDULES ROUTER: ${req.method} ${req.originalUrl} (${req.url})`);
  next();
});

// Routes for schedules (read-only)
scheduleRouter.get('/competition/:competitionId', getSchedulesByCompetition);
scheduleRouter.get('/:id', getScheduleById);

// Add a test route to verify the router is working
scheduleRouter.get('/test', (req, res) => {
  res.json({ message: 'Schedules router test route is working!' });
});

// Export the router
export { scheduleRouter };
