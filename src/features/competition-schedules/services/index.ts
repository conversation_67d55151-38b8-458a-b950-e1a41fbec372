import { dbConnect } from '../../../shared/lib/db';
import { CompetitionSchedule } from '../models/types';
import { forceToLocalDateTime } from '../../../shared/utils/date-formatter';

const competitionScheduleService = {
  getByCompetitionId: async (competitionId: number): Promise<CompetitionSchedule[]> => {
    // Assuming this matches your SQL command for schedules
    const sql = `select * from Entry4_uk_ScheduleView where compid = ?`;

    const schedules = await dbConnect.query<CompetitionSchedule[]>(sql, [competitionId]);

    return schedules;

    /*
    return schedules.map(schedule => {
      if (schedule.eventStartDate) {
        // const localTime = forceToLocalDateTime(schedule.eventStartDate);

        // console.log(typeof schedule.eventStartDate + ' ' + schedule.eventStartDate);

        // Format the date consistently using our utility function
        // schedule.eventStartDate = localTime;
      }

      return schedule;
    });
    */
  },

  getById: async (id: number): Promise<CompetitionSchedule | undefined> => {
    const sql = `
      SELECT 
        s.id,
        s.competition_id as competitionId,
        s.event_name as eventName,
        s.event_date as eventDate,
        s.start_time as startTime,
        s.end_time as endTime,
        s.location,
        s.description,
        c.id as categoryId,
        c.name as categoryName
      FROM 
        competition_schedules s
      LEFT JOIN 
        event_categories c ON s.category_id = c.id
      WHERE 
        s.id = ?
    `;

    const schedules = await dbConnect.query<CompetitionSchedule[]>(sql, [id]);
    return schedules[0];
  },
};

export { competitionScheduleService };
