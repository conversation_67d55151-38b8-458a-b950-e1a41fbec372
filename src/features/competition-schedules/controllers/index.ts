import { Request, Response, NextFunction } from 'express';
import { competitionScheduleService } from '../services';
import { logger } from '../../../shared/lib/logger';

export const getSchedulesByCompetition = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const competitionId = parseInt(req.params.competitionId, 10);
    
    if (isNaN(competitionId)) {
      res.status(400).json({ message: 'Invalid competition ID' });
      return;
    }
    
    const schedules = await competitionScheduleService.getByCompetitionId(competitionId);
    res.json({ data: schedules });
  } catch (error) {
    next(error);
  }
};

export const getScheduleById = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const id = parseInt(req.params.id, 10);
    
    if (isNaN(id)) {
      res.status(400).json({ message: 'Invalid schedule ID' });
      return;
    }
    
    const schedule = await competitionScheduleService.getById(id);
    
    if (!schedule) {
      res.status(404).json({ message: 'Schedule not found' });
      return;
    }
    
    res.json({ data: schedule });
  } catch (error) {
    next(error);
  }
};