import express from 'express';
import { getCompetitionById, getAllCompetitions, getCompetitionOnTheDay } from '../controllers';

const competitionRouter = express.Router();

// Add debugging middleware
// competitionRouter.use((req, res, next) => {
//   console.log(
//     `>>>>>>>> COMPETITIONS ROUTER DEBUGGING: ${req.method} ${req.originalUrl} (${req.url})`
//   );
//   next();
// });

// Add a catch-all route for debugging
// competitionRouter.use('*', (req, res, next) => {
//   console.log(`>>>>>>>> COMPETITIONS CATCH-ALL: ${req.method} ${req.originalUrl}`);
//   next();
// });

// Add a test route at the root level

// Standard routes
// competitionRouter.get('/', (req, res, next) => {
//   console.log('Root competitions route hit');
//   getAllCompetitions(req, res, next);
// });

competitionRouter.get('/:id/on-the-day', (req, res, next) => {
  console.log(`On-the-day route hit with ID: ${req.params.id}`);
  getCompetitionOnTheDay(req, res, next);
});

competitionRouter.get('/:id', (req, res, next) => {
  console.log(`Competition ID route hit with ID: ${req.params.id}`);
  getCompetitionById(req, res, next);
});

// Add a simple test route
competitionRouter.get('/test/test', (req, res) => {
  res.json({ message: 'Test route works' });
});

// Then uncomment your routes one by one to find the problematic one

// Temporary test route
competitionRouter.get('/debug-test', (req, res) => {
  console.log('Debug test route hit');
  res.json({ message: 'Debug test route is working!' });
});

export { competitionRouter };
