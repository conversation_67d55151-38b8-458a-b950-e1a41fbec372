import { CompetitionSchedule } from '../../competition-schedules/models/types';
import { CompetitionResult } from '../../competition-results/models/types';
import { CompetitionEntry } from '../../competition-entries/models/types';
import { CompetitionTeam } from '../../competition-teams/models/types';
import { CompetitionAthlete } from '../../competition-athletes/models/types';
import { CompetitionTrial } from '../../competition-trials/models/types';
import { CompetitionDetail } from '../services';
import { ExternalSocketMessage } from '../../websocket/types';

export interface CompetitionOnTheDay {
  competition: CompetitionDetail;
  schedules: CompetitionSchedule[];
  results: CompetitionResult[];
  entries: CompetitionEntry[];
  teams: CompetitionTeam[];
  athletes: CompetitionAthlete[];
  trials: CompetitionTrial[];
  socketMessages: ExternalSocketMessage[];
  cachedDataSummary: CachedDataSummary;
  competitionClientConfig: CompetitionClientConfig;
}

export interface CachedDataSummary {
  competitionId: number;
  cachedAt: Date;
  messageCount: number;
}

export interface CompetitionClientConfig {
  refreshIntervalMs: number;
}
