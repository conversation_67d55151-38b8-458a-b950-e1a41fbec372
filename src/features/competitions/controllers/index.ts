import { Request, Response, NextFunction } from 'express';
import { competitionService } from '../services';
import { competitionAggregatorService } from '../services/competition-aggregator';
import { logger } from '../../../shared/lib/logger';

const getAllCompetitions = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    console.log('Y Y Y YY Y getAllCompetitions() called');
    const competitions = await competitionService.getAll();
    res.json({ data: competitions });
  } catch (error) {
    next(error);
  }
};

const getCompetitionById = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    console.log('+ + + + + +getCompetitionById() called');
    const id = parseInt(req.params.id, 10);
    if (isNaN(id)) {
      res.status(400).json({ message: 'Invalid competition ID' });
      return;
    }

    const competition = await competitionService.getById(id);
    if (!competition) {
      res.status(404).json({ message: 'getCompetitionById() Competition not found' });
      return;
    }
    res.json({ data: competition });
  } catch (error) {
    next(error);
  }
};

const getCompetitionOnTheDay = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const id = parseInt(req.params.id, 10);
    if (isNaN(id)) {
      res.status(400).json({ message: 'Invalid competition ID' });
      return;
    }

    const competitionData = await competitionAggregatorService.getCompetitionOnTheDay(id);
    if (!competitionData) {
      res.status(404).json({ message: 'getCompetitionOnTheDay() Competition not found' });
      return;
    }
    res.json({ data: competitionData });
  } catch (error) {
    next(error);
  }
};

export { getAllCompetitions, getCompetitionById, getCompetitionOnTheDay };
