import { competitionCache } from './competition-cache';
import { competitionAggregatorService } from './competition-aggregator';
import { logger } from '../../../shared/lib/logger';

// Default refresh interval in milliseconds (5 minutes)
const DEFAULT_REFRESH_INTERVAL = 5 * 1000;

class CompetitionRefresher {
  private refreshInterval: number;
  private refreshTimer: NodeJS.Timeout | null = null;
  private isRefreshing = false;

  constructor(refreshIntervalMs = DEFAULT_REFRESH_INTERVAL) {
    this.refreshInterval = refreshIntervalMs;
  }

  start(): void {
    if (this.refreshTimer) {
      return;
    }

    logger.info(`Starting competition cache refresher with interval: ${this.refreshInterval}ms`);
    this.refreshTimer = setInterval(() => this.refreshCachedCompetitions(), this.refreshInterval);

    // Run immediately on start
    this.refreshCachedCompetitions();
  }

  stop(): void {
    if (this.refreshTimer) {
      clearInterval(this.refreshTimer);
      this.refreshTimer = null;
      logger.info('Competition cache refresher stopped');
    }
  }

  private async refreshCachedCompetitions(): Promise<void> {
    if (this.isRefreshing) {
      logger.info('Refresh already in progress, skipping');
      return;
    }

    this.isRefreshing = true;
    try {
      // Get all competition IDs from the cache
      const cachedCompetitionIds = Array.from(competitionCache.getCachedIds());
      logger.info(`Checking ${cachedCompetitionIds.length} cached competitions for expiration`);

      let refreshedCount = 0;
      // Process each competition
      for (const competitionId of cachedCompetitionIds) {
        try {
          // Check if the cache for this competition has expired
          if (competitionCache.isExpired(competitionId)) {
            // Force refresh by getting competition data again
            await competitionAggregatorService.getCompetitionOnTheDay(competitionId);
            logger.info(`Refreshed expired cache for competition ${competitionId}`);
            refreshedCount++;
          }
        } catch (error) {
          logger.error(`Failed to refresh competition ${competitionId}:`, error);
        }
      }

      // logger.info(
      //   `Refreshed ${refreshedCount} expired competitions out of ${cachedCompetitionIds.length} cached`
      // );
    } finally {
      this.isRefreshing = false;
    }
  }

  setRefreshInterval(intervalMs: number): void {
    this.refreshInterval = intervalMs;
    if (this.refreshTimer) {
      this.stop();
      this.start();
    }
    logger.info(`Competition refresher interval updated to ${intervalMs}ms`);
  }
}

// Create singleton instance
export const competitionRefresher = new CompetitionRefresher();
