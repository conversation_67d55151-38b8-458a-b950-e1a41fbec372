import { competitionService } from './index';
import { competitionScheduleService } from '../../competition-schedules/services';
import { competitionResultService } from '../../competition-results/services';
import { competitionEntryService } from '../../competition-entries/services';
import { competitionTeamService } from '../../competition-teams/services';
import { competitionAthleteService } from '../../competition-athletes/services';
import { competitionTrialService } from '../../competition-trials/services';
import { CompetitionOnTheDay } from '../models/types';
import { competitionCache } from './competition-cache';

// Map to track in-flight requests
const pendingRequests = new Map<number, Promise<CompetitionOnTheDay | null>>();

export const competitionAggregatorService = {
  getCompetitionOnTheDay: async (id: number): Promise<CompetitionOnTheDay | null> => {
    // Check cache first
    const cached = competitionCache.get(id);
    if (cached) {
      return cached;
    }

    // If there's already a request in progress for this ID, return that promise
    if (pendingRequests.has(id)) {
      console.log(`Request for competition ${id} already in progress, reusing promise`);
      return pendingRequests.get(id)!;
    }

    // Create a new promise for this request
    const promise = (async () => {
      try {
        // Get competition details
        const competition = await competitionService.getById(id);
        if (!competition) {
          return null;
        }

        // Get all related data in parallel
        const [schedules, results, entries, teams, athletes, trials] = await Promise.all([
          competitionScheduleService.getByCompetitionId(id),
          competitionResultService.getByCompetitionId(id),
          competitionEntryService.getByCompetitionId(id),
          competitionTeamService.getByCompetitionId(id),
          competitionAthleteService.getByCompetitionId(id),
          competitionTrialService.getByCompetitionId(id),
        ]);

        // We are "re-loading" the cache here, so clear the message queue.
        // But trying to handle scenarios where the cache is expired, but the socket
        // messages are still relevant as it was sent in as the cache was being built.
        // So we need to get "some" (last 5?) messages from the cache, before we clear it.
        const socketMessages = competitionCache.getMessages(id).slice(-5);

        // Combine all data into a single object
        const competitionData: CompetitionOnTheDay = {
          competition,
          schedules,
          results,
          entries,
          teams,
          athletes,
          trials,
          socketMessages,
          cachedDataSummary: {
            competitionId: id,
            cachedAt: new Date(),
            messageCount: socketMessages.length,
          },
          competitionClientConfig: {
            refreshIntervalMs: parseInt(
              process.env.CLIENT_COMPETITION_REFRESH_INTERVAL_MS || '60000',
              10
            ),
          },
        };

        // Store in cache
        competitionCache.set(id, competitionData);

        return competitionData;
      } finally {
        // Clean up the pending request when done
        pendingRequests.delete(id);
      }
    })();

    // Store the promise in the map
    pendingRequests.set(id, promise);

    return promise;
  },
};
