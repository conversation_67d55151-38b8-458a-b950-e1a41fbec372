import { logger } from '../../../shared/lib/logger';
import { competitionCache } from './competition-cache';

const DEFAULT_CLEANUP_INTERVAL = 300000; // 5 minutes

class QueueCleanupService {
  private cleanupInterval: number;
  private cleanupTimer: NodeJS.Timeout | null = null;
  private isRunning = false;

  constructor(cleanupIntervalMs = DEFAULT_CLEANUP_INTERVAL) {
    this.cleanupInterval = cleanupIntervalMs;
  }

  start(): void {
    if (this.cleanupTimer) {
      logger.warn('Queue cleanup service is already running');
      return;
    }

    logger.info(`Starting queue cleanup service with interval: ${this.cleanupInterval}ms`);
    this.cleanupTimer = setInterval(() => this.performCleanup(), this.cleanupInterval);

    // Run immediately on start
    this.performCleanup();
  }

  stop(): void {
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer);
      this.cleanupTimer = null;
      logger.info('Queue cleanup service stopped');
    }
  }

  private async performCleanup(): Promise<void> {
    if (this.isRunning) {
      logger.debug('Queue cleanup already in progress, skipping');
      return;
    }

    this.isRunning = true;
    try {
      const startTime = Date.now();
      const result = competitionCache.performGlobalCleanup();
      const duration = Date.now() - startTime;

      // Only log if there was actual cleanup work done
      if (result.messagesExpired > 0 || result.emptyQueuesRemoved > 0) {
        logger.info(
          `Queue cleanup completed in ${duration}ms: ` +
          `${result.messagesExpired} messages expired, ` +
          `${result.emptyQueuesRemoved} empty queues removed from ` +
          `${result.queuesProcessed} queues`
        );
      } else {
        logger.debug(`Queue cleanup completed in ${duration}ms: no cleanup needed`);
      }

      // Log queue metrics periodically (every 10 cleanups)
      const metrics = competitionCache.getQueueMetrics();
      if (metrics.totalMessagesProcessed % 10 === 0 && metrics.totalMessagesProcessed > 0) {
        logger.info(
          `Queue metrics: ${metrics.totalQueuedMessages} messages in ${metrics.activeQueues} queues, ` +
          `peak: ${metrics.peakQueueSize}, processed: ${metrics.totalMessagesProcessed}, ` +
          `evicted: ${metrics.totalMessagesEvicted}, expired: ${metrics.totalMessagesExpired}`
        );
      }
    } catch (error) {
      logger.error('Error during queue cleanup:', error);
    } finally {
      this.isRunning = false;
    }
  }

  setCleanupInterval(intervalMs: number): void {
    this.cleanupInterval = intervalMs;
    if (this.cleanupTimer) {
      this.stop();
      this.start();
    }
    logger.info(`Queue cleanup interval updated to ${intervalMs}ms`);
  }

  /**
   * Get cleanup service status
   */
  getStatus(): {
    isRunning: boolean;
    interval: number;
    nextCleanup?: Date;
  } {
    return {
      isRunning: this.cleanupTimer !== null,
      interval: this.cleanupInterval,
      nextCleanup: this.cleanupTimer ? new Date(Date.now() + this.cleanupInterval) : undefined,
    };
  }
}

// Create singleton instance
export const queueCleanupService = new QueueCleanupService(
  parseInt(process.env.QUEUE_CLEANUP_INTERVAL_MS || '300000', 10)
);
