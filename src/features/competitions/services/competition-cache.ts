import { CompetitionOnTheDay } from '../models/types';
import { logger } from '../../../shared/lib/logger';
import {
  ExternalMessageActionNotInterestedIn,
  ExternalSocketMessage,
  isInterestedExternalAction,
} from '../../websocket/types';
import crypto from 'crypto';

// Queue configuration interface
interface QueueConfig {
  maxSize: number;
  maxAgeMs: number;
  warningThreshold: number;
}

// Enhanced message with timestamp for expiry tracking
interface TimestampedMessage extends ExternalSocketMessage {
  queuedAt: number;
}

// Simple in-memory cache implementation
class CompetitionCache {
  private cache: Map<number, { data: CompetitionOnTheDay; timestamp: number }> = new Map();
  private readonly TTL_MS = parseInt(process.env.COMPETITION_CACHE_TTL || '60000', 10); // Cache TTL from env or default to 1 minute

  // Message queue to store messages by competition ID with enhanced tracking
  private messageQueues: Map<number, TimestampedMessage[]> = new Map();

  // Queue configuration with environment variable support
  private readonly queueConfig: QueueConfig = {
    maxSize: parseInt(process.env.MESSAGE_QUEUE_MAX_SIZE || '1000', 10),
    maxAgeMs: parseInt(process.env.MESSAGE_QUEUE_MAX_AGE_MS || '3600000', 10), // 1 hour default
    warningThreshold: parseInt(process.env.MESSAGE_QUEUE_WARNING_THRESHOLD || '800', 10), // 80% of max size
  };

  // Queue metrics for monitoring
  private queueMetrics = {
    totalMessagesProcessed: 0,
    totalMessagesEvicted: 0,
    totalMessagesExpired: 0,
    peakQueueSize: 0,
    lastCleanupTime: Date.now(),
  };

  get(competitionId: number): CompetitionOnTheDay | null {
    const cached = this.cache.get(competitionId);
    if (!cached) {
      logger.info(`Cache miss for competition ID: ${competitionId}`);
      return null;
    }

    // Check if cache entry is still valid
    if (Date.now() - cached.timestamp > this.TTL_MS) {
      logger.info(`Cache expired for competition ID: ${competitionId}`);
      this.cache.delete(competitionId);
      return null;
    }

    // Get the latest messages from the queue
    const messages = this.getMessages(competitionId);

    // Update the cached data with the latest messages
    cached.data.socketMessages = messages;

    logger.info(`Cache hit for competition ID: ${competitionId}`);
    return cached.data;
  }

  set(competitionId: number, data: CompetitionOnTheDay): void {
    logger.info(`Caching data for competition ID: ${competitionId}`);

    // Get any existing messages for this competition
    const messages = this.getMessages(competitionId);

    // Ensure the data has a socketMessages property
    if (!data.socketMessages) {
      data.socketMessages = [];
    }

    // Add existing messages to the data
    data.socketMessages = [...messages];

    this.cache.set(competitionId, { data, timestamp: Date.now() });
  }

  invalidate(competitionId: number): void {
    logger.info(`Invalidating cache for competition ID: ${competitionId}`);
    this.cache.delete(competitionId);
    // Also clear the message queue when invalidating cache
    this.clearMessageQueue(competitionId);
  }

  /**
   * Cleans expired messages from a queue based on age
   * @param queue The queue to clean
   * @returns Number of messages removed
   */
  private cleanExpiredMessages(queue: TimestampedMessage[]): number {
    const now = Date.now();
    const initialLength = queue.length;

    // Remove messages older than maxAgeMs
    const validMessages = queue.filter(msg => {
      const age = now - msg.queuedAt;
      return age <= this.queueConfig.maxAgeMs;
    });

    const removedCount = initialLength - validMessages.length;
    if (removedCount > 0) {
      queue.splice(0, queue.length, ...validMessages);
      this.queueMetrics.totalMessagesExpired += removedCount;
      logger.info(`Cleaned ${removedCount} expired messages from queue`);
    }

    return removedCount;
  }

  /**
   * Enforces queue size limits using FIFO eviction
   * @param queue The queue to manage
   * @param competitionId The competition ID for logging
   * @returns Number of messages evicted
   */
  private enforceQueueSizeLimit(queue: TimestampedMessage[], competitionId: number): number {
    if (queue.length <= this.queueConfig.maxSize) {
      return 0;
    }

    const excessCount = queue.length - this.queueConfig.maxSize;
    const evictedMessages = queue.splice(0, excessCount);

    this.queueMetrics.totalMessagesEvicted += excessCount;
    logger.warn(
      `Queue size limit exceeded for competition ${competitionId}. ` +
      `Evicted ${excessCount} oldest messages. Queue size: ${queue.length}/${this.queueConfig.maxSize}`
    );

    return excessCount;
  }

  /**
   * Checks if queue size is approaching warning threshold
   * @param queue The queue to check
   * @param competitionId The competition ID for logging
   */
  private checkQueueWarningThreshold(queue: TimestampedMessage[], competitionId: number): void {
    if (queue.length >= this.queueConfig.warningThreshold) {
      logger.warn(
        `Queue size warning for competition ${competitionId}: ` +
        `${queue.length}/${this.queueConfig.maxSize} messages ` +
        `(threshold: ${this.queueConfig.warningThreshold})`
      );
    }
  }

  /**
   * Stores a socket message in the queue for a specific competition.  These
   * will be sent to clients when they subscribe.
   * @param competitionId The competition ID
   * @param message The message to store
   */
  storeMessage(competitionId: number, message: ExternalSocketMessage): void {
    // If the message is one of the message types we need to ignore just return.

    //  If no key exists on the message generate a unique GUID
    if (!message.key) {
      message.key = 's-' + crypto.randomUUID();
    }

    // only queue messages we are interested in.
    if (!isInterestedExternalAction(message.action)) {
      // logger.info(`Not interested in message: ${message.action}`);
      return;
    }

    if (!this.messageQueues.has(competitionId)) {
      this.messageQueues.set(competitionId, []);
    }

    const queue = this.messageQueues.get(competitionId)!;

    // Clean expired messages first
    this.cleanExpiredMessages(queue);

    // Create timestamped message
    const timestampedMessage: TimestampedMessage = {
      ...message,
      queuedAt: Date.now(),
    };

    // Add the new message
    queue.push(timestampedMessage);
    this.queueMetrics.totalMessagesProcessed++;

    // Update peak queue size metric
    if (queue.length > this.queueMetrics.peakQueueSize) {
      this.queueMetrics.peakQueueSize = queue.length;
    }

    // Enforce size limits (FIFO eviction)
    this.enforceQueueSizeLimit(queue, competitionId);

    // Check warning threshold
    this.checkQueueWarningThreshold(queue, competitionId);

    logger.info(
      `Stored message in queue for competition ${competitionId} (${message.action}). ` +
      `Queue size: ${queue.length}/${this.queueConfig.maxSize}`
    );

    // Update the cached data with the new message if it exists
    const cached = this.cache.get(competitionId);
    if (cached) {
      if (!cached.data.socketMessages) {
        cached.data.socketMessages = [];
      }
      // Only add the original message (without queuedAt) to cached data
      cached.data.socketMessages.push(message);
      this.cache.set(competitionId, { data: cached.data, timestamp: cached.timestamp });
    }
  }

  /**
   * Clears the message queue for a specific competition
   * @param competitionId The competition ID
   */
  clearMessageQueue(competitionId: number): void {
    const queue = this.messageQueues.get(competitionId);
    const clearedCount = queue ? queue.length : 0;

    this.messageQueues.delete(competitionId);
    logger.info(`Cleared message queue for competition ${competitionId} (${clearedCount} messages)`);

    // Also clear the messages in the cached data if it exists
    const cached = this.cache.get(competitionId);
    if (cached) {
      cached.data.socketMessages = [];
      this.cache.set(competitionId, { data: cached.data, timestamp: cached.timestamp });
    }
  }

  /**
   * Gets all messages from the queue for a specific competition
   * @param competitionId The competition ID
   * @returns Array of messages (without internal timestamps)
   */
  getMessages(competitionId: number): ExternalSocketMessage[] {
    const queue = this.messageQueues.get(competitionId);
    if (!queue) {
      return [];
    }

    // Clean expired messages before returning
    this.cleanExpiredMessages(queue);

    // Return messages without the internal queuedAt timestamp
    return queue.map(({ queuedAt, ...message }) => message);
  }

  /**
   * Gets queue statistics for a specific competition
   * @param competitionId The competition ID
   * @returns Queue statistics object
   */
  getQueueStats(competitionId: number): {
    size: number;
    maxSize: number;
    warningThreshold: number;
    oldestMessageAge?: number;
    newestMessageAge?: number;
  } {
    const queue = this.messageQueues.get(competitionId) || [];
    const now = Date.now();

    const stats = {
      size: queue.length,
      maxSize: this.queueConfig.maxSize,
      warningThreshold: this.queueConfig.warningThreshold,
    };

    if (queue.length > 0) {
      return {
        ...stats,
        oldestMessageAge: now - queue[0].queuedAt,
        newestMessageAge: now - queue[queue.length - 1].queuedAt,
      };
    }

    return stats;
  }

  /**
   * Gets global queue metrics for monitoring
   * @returns Global queue metrics
   */
  getQueueMetrics(): typeof this.queueMetrics & {
    activeQueues: number;
    totalQueuedMessages: number;
    config: QueueConfig;
  } {
    const totalQueuedMessages = Array.from(this.messageQueues.values())
      .reduce((total, queue) => total + queue.length, 0);

    return {
      ...this.queueMetrics,
      activeQueues: this.messageQueues.size,
      totalQueuedMessages,
      config: this.queueConfig,
    };
  }

  /**
   * Performs cleanup on all queues (removes expired messages)
   * @returns Summary of cleanup operation
   */
  performGlobalCleanup(): {
    queuesProcessed: number;
    messagesExpired: number;
    emptyQueuesRemoved: number;
  } {
    const now = Date.now();
    let queuesProcessed = 0;
    let messagesExpired = 0;
    let emptyQueuesRemoved = 0;
    const queuesToRemove: number[] = [];

    for (const [competitionId, queue] of this.messageQueues.entries()) {
      queuesProcessed++;
      const expiredCount = this.cleanExpiredMessages(queue);
      messagesExpired += expiredCount;

      // Mark empty queues for removal
      if (queue.length === 0) {
        queuesToRemove.push(competitionId);
      }
    }

    // Remove empty queues
    for (const competitionId of queuesToRemove) {
      this.messageQueues.delete(competitionId);
      emptyQueuesRemoved++;
    }

    this.queueMetrics.lastCleanupTime = now;

    if (messagesExpired > 0 || emptyQueuesRemoved > 0) {
      logger.info(
        `Global queue cleanup completed: ${queuesProcessed} queues processed, ` +
        `${messagesExpired} messages expired, ${emptyQueuesRemoved} empty queues removed`
      );
    }

    return { queuesProcessed, messagesExpired, emptyQueuesRemoved };
  }

  getCachedIds(): Set<number> {
    return new Set(this.cache.keys());
  }

  isExpired(competitionId: number): boolean {
    const cached = this.cache.get(competitionId);
    if (!cached) {
      return false; // Not in cache, so not expired
    }

    // Check if cache entry is expired
    // logger.info(`Cache expired for competition ID: ${competitionId}`);
    const timeDiff = Date.now() - cached.timestamp;

    const isExp = timeDiff > this.TTL_MS;

    logger.info(
      `Comp: ${competitionId} time difference: ${timeDiff}ms, TTL: ${this.TTL_MS}ms, Expired: ${isExp}`
    );

    return isExp;
  }

  /**
   * Get all cached competitions with metadata
   * @returns Array of cached competition objects with their IDs and cache timestamps
   */
  getAllCachedCompetitions(): Array<{ id: number; cachedAt: Date }> {
    const result: Array<{ id: number; cachedAt: Date }> = [];

    // Get all competition IDs from the cache
    const cachedIds = this.getCachedIds();

    // For each ID, create an object with the ID and cache timestamp
    for (const id of cachedIds) {
      const competition = this.get(id);
      if (competition) {
        result.push({
          id,
          cachedAt: new Date(this.cache.get(id)?.timestamp || Date.now()),
        });
      }
    }

    return result;
  }
}

export const competitionCache = new CompetitionCache();
