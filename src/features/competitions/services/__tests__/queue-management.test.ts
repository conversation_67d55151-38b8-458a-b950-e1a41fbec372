import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { competitionCache } from '../competition-cache';
import { ExternalSocketMessage } from '../../../websocket/types';

// Mock logger to avoid console output during tests
vi.mock('../../../../shared/lib/logger', () => ({
  logger: {
    info: vi.fn(),
    warn: vi.fn(),
    error: vi.fn(),
    debug: vi.fn(),
  },
}));

describe('Queue Management', () => {
  const testCompetitionId = 123;
  
  beforeEach(() => {
    // Clear any existing cache/queues
    competitionCache.clearMessageQueue(testCompetitionId);
    vi.clearAllMocks();
  });

  afterEach(() => {
    // Clean up after each test
    competitionCache.clearMessageQueue(testCompetitionId);
  });

  const createTestMessage = (action: string, key?: string): ExternalSocketMessage => ({
    key: key || `test-${Date.now()}`,
    comp: { id: testCompetitionId },
    action: action as any,
    deviceKey: 1,
    securityKey: 'test',
    domain: 'test',
    payload: { test: true },
    utcTime: new Date().toISOString(),
  });

  describe('Queue Size Limits', () => {
    it('should store messages up to the configured limit', () => {
      // Store messages up to the limit (assuming default config)
      for (let i = 0; i < 10; i++) {
        const message = createTestMessage('result-update', `msg-${i}`);
        competitionCache.storeMessage(testCompetitionId, message);
      }

      const messages = competitionCache.getMessages(testCompetitionId);
      expect(messages.length).toBe(10);
    });

    it('should provide queue statistics', () => {
      // Store a few messages
      for (let i = 0; i < 5; i++) {
        const message = createTestMessage('result-update', `msg-${i}`);
        competitionCache.storeMessage(testCompetitionId, message);
      }

      const stats = competitionCache.getQueueStats(testCompetitionId);
      expect(stats.size).toBe(5);
      expect(stats.maxSize).toBeGreaterThan(0);
      expect(stats.warningThreshold).toBeGreaterThan(0);
      expect(stats.oldestMessageAge).toBeGreaterThanOrEqual(0);
      expect(stats.newestMessageAge).toBeGreaterThanOrEqual(0);
    });

    it('should provide global queue metrics', () => {
      // Store messages in multiple competitions
      for (let compId = 1; compId <= 3; compId++) {
        for (let i = 0; i < 2; i++) {
          const message = createTestMessage('result-update', `comp${compId}-msg-${i}`);
          message.comp.id = compId;
          competitionCache.storeMessage(compId, message);
        }
      }

      const metrics = competitionCache.getQueueMetrics();
      expect(metrics.activeQueues).toBe(3);
      expect(metrics.totalQueuedMessages).toBe(6);
      expect(metrics.totalMessagesProcessed).toBeGreaterThanOrEqual(6);
      expect(metrics.config).toBeDefined();
      expect(metrics.config.maxSize).toBeGreaterThan(0);
    });
  });

  describe('Message Filtering', () => {
    it('should only store interested message types', () => {
      // Store an interested message
      const interestedMessage = createTestMessage('result-update');
      competitionCache.storeMessage(testCompetitionId, interestedMessage);

      // Try to store a non-interested message
      const nonInterestedMessage = createTestMessage('ping');
      competitionCache.storeMessage(testCompetitionId, nonInterestedMessage);

      const messages = competitionCache.getMessages(testCompetitionId);
      expect(messages.length).toBe(1);
      expect(messages[0].action).toBe('result-update');
    });
  });

  describe('Queue Cleanup', () => {
    it('should perform global cleanup', () => {
      // Store some messages
      for (let i = 0; i < 3; i++) {
        const message = createTestMessage('result-update', `msg-${i}`);
        competitionCache.storeMessage(testCompetitionId, message);
      }

      const result = competitionCache.performGlobalCleanup();
      expect(result.queuesProcessed).toBeGreaterThanOrEqual(1);
      expect(result.messagesExpired).toBeGreaterThanOrEqual(0);
      expect(result.emptyQueuesRemoved).toBeGreaterThanOrEqual(0);
    });

    it('should clear message queue completely', () => {
      // Store some messages
      for (let i = 0; i < 3; i++) {
        const message = createTestMessage('result-update', `msg-${i}`);
        competitionCache.storeMessage(testCompetitionId, message);
      }

      expect(competitionCache.getMessages(testCompetitionId).length).toBe(3);

      competitionCache.clearMessageQueue(testCompetitionId);
      expect(competitionCache.getMessages(testCompetitionId).length).toBe(0);
    });
  });

  describe('Message Retrieval', () => {
    it('should return messages without internal timestamps', () => {
      const originalMessage = createTestMessage('result-update');
      competitionCache.storeMessage(testCompetitionId, originalMessage);

      const retrievedMessages = competitionCache.getMessages(testCompetitionId);
      expect(retrievedMessages.length).toBe(1);
      
      const retrievedMessage = retrievedMessages[0];
      expect(retrievedMessage.key).toBe(originalMessage.key);
      expect(retrievedMessage.action).toBe(originalMessage.action);
      expect(retrievedMessage.comp.id).toBe(originalMessage.comp.id);
      
      // Should not have internal queuedAt timestamp
      expect((retrievedMessage as any).queuedAt).toBeUndefined();
    });
  });
});
