import { RowDataPacket } from 'mysql2/promise';
import { dbConnect } from '../../../shared/lib/db';

interface Competition extends RowDataPacket {
  id: number;
  name: string;
  date: Date;
  locationid: number;
  compclubid: number;
  // Add other fields as needed
}

interface CompetitionDetail extends RowDataPacket {
  compId: number;
  compName: string;
  compdate: Date;
  locId: number;
  locName: string;
  compclubid: number;
  organiser: string;
  logo: string;
  // Add other fields as needed
}

const competitionService = {
  getAll: async (): Promise<Competition[]> => {
    return await dbConnect.query<Competition[]>('SELECT * FROM Entry4_uk_Competition');
  },

  getById: async (id: number): Promise<CompetitionDetail | undefined> => {
    // const sql = `select
    //   c.id compId,
    //   c.name compName,
    //   c.date compdate,
    //   c.locationid locId,
    //   l.location locName,
    //   c.compclubid,
    //   cc.club organiser,
    //   cc.logo
    //   from
    //   Entry4_uk_Competition c,
    //   Entry4_location l,
    //   Entry4_CompClub cc
    //   where c.locationid = l.id and cc.id = c.compclubid and c.id = ?`;

    const sql = `Select * from Entry4_uk_CompetitionView where compId = ?`;

    const competitions = await dbConnect.query<CompetitionDetail[]>(sql, [id]);
    return competitions[0];
  },
};

export { competitionService, Competition, CompetitionDetail };
