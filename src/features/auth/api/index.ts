import express from 'express';
import passport from 'passport';
import session from 'express-session';
import { Strategy as GoogleStrategy, StrategyOptions } from 'passport-google-oauth20';
import { verifyToken, requireAuth, generateToken } from '../middleware/auth';

// Extend the Express.Session interface to include redirectUrl
declare module 'express-session' {
  interface SessionData {
    redirectUrl?: string;
  }
}

const authRouter = express.Router();

// Add session support
authRouter.use(
  session({
    secret: process.env.SESSION_SECRET || process.env.JWT_SECRET || 'your-secret-key',
    resave: false,
    saveUninitialized: false,
    cookie: {
      secure: process.env.NODE_ENV === 'production',
      maxAge: 24 * 60 * 60 * 1000, // 24 hours
    },
  })
);

// Initialize passport and restore authentication state from session
authRouter.use(passport.initialize());
authRouter.use(passport.session());

// Serialize and deserialize user
passport.serializeUser((user: any, done) => {
  done(null, user);
});

passport.deserializeUser((user: any, done) => {
  done(null, user);
});

// Configure Google Strategy
passport.use(
  new GoogleStrategy(
    {
      clientID: process.env.GOOGLE_CLIENT_ID!,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET!,
      callbackURL: '/api/auth/google/callback',
    },
    (
      accessToken: string,
      refreshToken: string,
      profile: any,
      done: (error: any, user?: any) => void
    ) => {
      // Here you would typically look up the user in your database
      // and determine their roles based on your business logic

      // For example, you might have admin emails configured:
      const adminEmails = (process.env.ADMIN_EMAILS || '').split(',');
      const isAdmin =
        profile.emails && profile.emails[0] && adminEmails.includes(profile.emails[0].value);

      // Create user object with appropriate roles
      const user = {
        id: profile.id,
        email: profile.emails[0].value,
        name: profile.displayName,
        roles: isAdmin ? ['admin'] : ['user'],
      };

      // In a real application, you would save this user to your database
      // or update their information if they already exist

      return done(null, user);
    }
  )
);

// Google auth routes
authRouter.get('/google', (req, res, next) => {
  // Store the redirect URL in the session
  if (req.query.redirect_url) {
    if (req.session) {
      req.session.redirectUrl = req.query.redirect_url.toString();
    }
  }

  // Continue with Google authentication
  passport.authenticate('google', {
    scope: ['profile', 'email'],
    state: req.query.redirect_url
      ? Buffer.from(req.query.redirect_url.toString()).toString('base64')
      : undefined,
  })(req, res, next);
});
authRouter.get(
  '/google/callback',
  passport.authenticate('google', { failureRedirect: '/login' }),
  (req, res) => {
    // Generate JWT token
    const token = generateToken(req.user);

    // Get the redirect URL from state parameter or environment variable
    let clientRedirectUrl =
      process.env.CLIENT_REDIRECT_URL + '/#/auth-success' || 'http://localhost:5173/auth-success';

    // If state parameter exists, decode it to get the redirect URL
    if (req.query.state) {
      try {
        const decodedState = Buffer.from(req.query.state.toString(), 'base64').toString();
        if (decodedState) {
          clientRedirectUrl = decodedState;
        }
      } catch (error) {
        console.error('Error decoding state parameter:', error);
      }
    }

    console.log('Google callback hit============================');
    console.log('State:', req.query.state);
    console.log('Redirect URL:', clientRedirectUrl);

    const url = `${clientRedirectUrl}?token=${token}`;
    console.log('Redirecting to:', url);
    console.log('Google callback hit============================');

    // Redirect to the client application with the token
    res.redirect(`${clientRedirectUrl}?token=${token}`);
  }
);

// Verify token endpoint
authRouter.get('/verify', verifyToken, (req, res) => {
  if (!req.user) {
    return res.status(401).json({ message: 'Authentication required' });
  }

  console.log('User from token:', req.user);

  const { getAllPermissionsForUser } = require('../config/roles');
  const permissions = getAllPermissionsForUser(req.user);

  console.log('Calculated permissions:', permissions);

  res.json({
    user: {
      id: req.user.id,
      email: req.user.email,
      name: req.user.name,
      roles: req.user.roles || [],
    },
    permissions,
  });
});

// Get user permissions
authRouter.get('/permissions', verifyToken, (req, res) => {
  if (!req.user) {
    return res.status(401).json({ message: 'Authentication required' });
  }

  const { getAllPermissionsForUser } = require('../config/roles');
  const permissions = getAllPermissionsForUser(req.user);

  res.json({
    user: {
      id: req.user.id,
      email: req.user.email,
      roles: req.user.roles || [],
    },
    permissions,
  });
});

export { authRouter, requireAuth };
