// Define permissions
export const PERMISSIONS = {
  // Competition permissions
  VIEW_COMPETITIONS: 'view:competitions',
  CREATE_COMPETITION: 'create:competition',
  EDIT_COMPETITION: 'edit:competition',
  DELETE_COMPETITION: 'delete:competition',

  // Admin permissions
  VIEW_ADMIN_DASHBOARD: 'view:admin-dashboard',
  MANAGE_USERS: 'manage:users',

  // Other resource permissions
  VIEW_ENTRIES: 'view:entries',
  EDIT_ENTRIES: 'edit:entries',
  // Add more permissions as needed
};

// Define roles with their permissions
export const ROLES = {
  ADMIN: {
    name: 'admin',
    permissions: Object.values(PERMISSIONS), // <PERSON><PERSON> has all permissions
  },
  EDITOR: {
    name: 'editor',
    permissions: [
      PERMISSIONS.VIEW_COMPETITIONS,
      PERMISSIONS.CREATE_COMPETITION,
      PERMISSIONS.EDIT_COMPETITION,
      PERMISSIONS.VIEW_ENTRIES,
      PERMISSIONS.EDIT_ENTRIES,
    ],
  },
  USER: {
    name: 'user',
    permissions: [PERMISSIONS.VIEW_COMPETITIONS, PERMISSIONS.VIEW_ENTRIES],
  },
};

// Helper function to get permissions for a role
export const getPermissionsForRole = (roleName: string): string[] => {
  const role = Object.values(ROLES).find(r => r.name === roleName);
  return role ? role.permissions : [];
};

// Helper function to get all permissions for a user with multiple roles
export const getAllPermissionsForUser = (user: any): string[] => {
  if (!user.roles || !Array.isArray(user.roles)) {
    return [];
  }

  // Get permissions from all roles
  const rolePermissions = user.roles.flatMap((roleName: string) => getPermissionsForRole(roleName));

  // Add direct permissions if any
  const directPermissions = user.permissions || [];

  // Combine and remove duplicates
  return [...new Set([...rolePermissions, ...directPermissions])];
};
