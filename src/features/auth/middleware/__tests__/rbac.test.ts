import { describe, it, expect, vi, beforeEach } from 'vitest';
import { Request, Response, NextFunction } from 'express';
import { hasRole, hasPermission, hasAnyPermission, hasAllPermissions } from '../rbac';
import { logger } from '../../../../shared/lib/logger';

// Mock the logger
vi.mock('../../../../shared/lib/logger', () => ({
  logger: {
    warn: vi.fn(),
    info: vi.fn(),
    error: vi.fn(),
    debug: vi.fn(),
  },
}));

// Mock the roles config
vi.mock('../../config/roles', () => ({
  getAllPermissionsForUser: vi.fn(),
  ROLES: {
    ADMIN: { name: 'admin' },
    USER: { name: 'user' },
  },
}));

// Import the mocked function
import { getAllPermissionsForUser } from '../../config/roles';

describe('RBAC Middleware Logging', () => {
  let mockReq: Partial<Request>;
  let mockRes: Partial<Response>;
  let mockNext: NextFunction;

  beforeEach(() => {
    vi.clearAllMocks();

    mockReq = {
      ip: '***********',
      originalUrl: '/admin/users',
      url: '/admin/users',
      method: 'GET',
      get: vi.fn((header: string) => {
        if (header === 'User-Agent') return 'Mozilla/5.0 Test Browser';
        if (header === 'set-cookie') return ['test-cookie'];
        return undefined;
      }) as any,
    };

    mockRes = {
      status: vi.fn().mockReturnThis(),
      json: vi.fn().mockReturnThis(),
    };

    mockNext = vi.fn() as unknown as NextFunction;
  });

  describe('hasRole middleware', () => {
    it('should log when user is not authenticated', () => {
      const middleware = hasRole('admin');

      // No user in request
      mockReq.user = undefined;

      middleware(mockReq as Request, mockRes as Response, mockNext);

      expect(logger.warn).toHaveBeenCalledWith(
        'Access denied: Authentication required',
        expect.objectContaining({
          userId: 'unknown',
          email: 'unknown',
          roles: [],
          ip: '***********',
          userAgent: 'Mozilla/5.0 Test Browser',
          route: '/admin/users',
          method: 'GET',
          reason: 'no_authentication',
          requiredRole: 'admin',
        })
      );

      expect(mockRes.status).toHaveBeenCalledWith(401);
    });

    it('should log when user has no roles assigned', () => {
      const middleware = hasRole('admin');

      mockReq.user = {
        id: 'user123',
        email: '<EMAIL>',
        roles: null, // No roles assigned
      };

      middleware(mockReq as Request, mockRes as Response, mockNext);

      expect(logger.warn).toHaveBeenCalledWith(
        'Access denied: No roles assigned to user',
        expect.objectContaining({
          userId: 'user123',
          email: '<EMAIL>',
          reason: 'no_roles_assigned',
          requiredRole: 'admin',
          userRoles: null,
        })
      );

      expect(mockRes.status).toHaveBeenCalledWith(403);
    });

    it('should log when user has insufficient role', () => {
      const middleware = hasRole('admin');

      mockReq.user = {
        id: 'user123',
        email: '<EMAIL>',
        roles: ['user'], // User role, but admin required
      };

      middleware(mockReq as Request, mockRes as Response, mockNext);

      expect(logger.warn).toHaveBeenCalledWith(
        'Access denied: Insufficient role',
        expect.objectContaining({
          userId: 'user123',
          email: '<EMAIL>',
          roles: ['user'],
          reason: 'insufficient_role',
          requiredRole: 'admin',
          userRoles: ['user'],
        })
      );

      expect(mockRes.status).toHaveBeenCalledWith(403);
    });

    it('should not log when user has sufficient role', () => {
      const middleware = hasRole('admin');

      mockReq.user = {
        id: 'admin123',
        email: '<EMAIL>',
        roles: ['admin'], // Has admin role
      };

      middleware(mockReq as Request, mockRes as Response, mockNext);

      expect(logger.warn).not.toHaveBeenCalled();
      expect(mockNext).toHaveBeenCalled();
    });
  });

  describe('hasPermission middleware', () => {
    it('should log when user lacks required permission', () => {
      (getAllPermissionsForUser as any).mockReturnValue(['view:competitions']); // User only has view permission

      const middleware = hasPermission('manage:users');

      mockReq.user = {
        id: 'user123',
        email: '<EMAIL>',
        roles: ['user'],
      };

      middleware(mockReq as Request, mockRes as Response, mockNext);

      expect(logger.warn).toHaveBeenCalledWith(
        'Access denied: Insufficient permissions',
        expect.objectContaining({
          userId: 'user123',
          email: '<EMAIL>',
          reason: 'insufficient_permissions',
          requiredPermission: 'manage:users',
          userPermissions: ['view:competitions'],
        })
      );

      expect(mockRes.status).toHaveBeenCalledWith(403);
    });
  });

  describe('hasAllPermissions middleware', () => {
    it('should log missing permissions when user lacks some required permissions', () => {
      (getAllPermissionsForUser as any).mockReturnValue(['view:competitions', 'edit:competitions']);

      const middleware = hasAllPermissions(['view:competitions', 'edit:competitions', 'delete:competitions']);

      mockReq.user = {
        id: 'user123',
        email: '<EMAIL>',
        roles: ['editor'],
      };

      middleware(mockReq as Request, mockRes as Response, mockNext);

      expect(logger.warn).toHaveBeenCalledWith(
        'Access denied: User lacks some required permissions',
        expect.objectContaining({
          userId: 'user123',
          email: '<EMAIL>',
          reason: 'insufficient_permissions_all',
          requiredPermissions: ['view:competitions', 'edit:competitions', 'delete:competitions'],
          userPermissions: ['view:competitions', 'edit:competitions'],
          missingPermissions: ['delete:competitions'],
        })
      );

      expect(mockRes.status).toHaveBeenCalledWith(403);
    });
  });
});
