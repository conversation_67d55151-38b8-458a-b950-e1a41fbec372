import { Request, Response, NextFunction } from 'express';
import { getAllPermissionsForUser, ROLES } from '../config/roles';
import { logger } from '../../../shared/lib/logger';

// Helper function to extract user context for logging
const getUserContext = (req: Request) => {
  return {
    userId: req.user?.id || 'unknown',
    email: req.user?.email || 'unknown',
    roles: req.user?.roles || [],
    ip: req.ip || req.connection?.remoteAddress || 'unknown',
    userAgent: req.get('User-Agent') || 'unknown',
    route: req.originalUrl || req.url,
    method: req.method,
  };
};

// Middleware to check if user has a specific role
export const hasRole = (role: string) => {
  return (req: Request, res: Response, next: NextFunction) => {
    const userContext = getUserContext(req);

    if (!req.user) {
      logger.warn('Access denied: Authentication required', {
        ...userContext,
        reason: 'no_authentication',
        requiredRole: role,
      });
      return res.status(401).json({ message: 'Authentication required' });
    }

    if (!req.user.roles || !Array.isArray(req.user.roles)) {
      logger.warn('Access denied: No roles assigned to user', {
        ...userContext,
        reason: 'no_roles_assigned',
        requiredRole: role,
        userRoles: req.user.roles,
      });
      return res.status(403).json({ message: 'Forbidden: No roles assigned' });
    }

    if (req.user.roles.includes(role)) {
      return next();
    }

    logger.warn('Access denied: Insufficient role', {
      ...userContext,
      reason: 'insufficient_role',
      requiredRole: role,
      userRoles: req.user.roles,
    });
    return res.status(403).json({ message: 'Forbidden: Insufficient role' });
  };
};

// Middleware to check if user has admin role
export const isAdmin = (req: Request, res: Response, next: NextFunction) => {
  return hasRole(ROLES.ADMIN.name)(req, res, next);
};

// Middleware to check if user has a specific permission
export const hasPermission = (permission: string) => {
  return (req: Request, res: Response, next: NextFunction) => {
    const userContext = getUserContext(req);

    if (!req.user) {
      logger.warn('Access denied: Authentication required', {
        ...userContext,
        reason: 'no_authentication',
        requiredPermission: permission,
      });
      return res.status(401).json({ message: 'Authentication required' });
    }

    const userPermissions = getAllPermissionsForUser(req.user);

    if (userPermissions.includes(permission)) {
      return next();
    }

    logger.warn('Access denied: Insufficient permissions', {
      ...userContext,
      reason: 'insufficient_permissions',
      requiredPermission: permission,
      userPermissions: userPermissions,
    });
    return res.status(403).json({ message: 'Forbidden: Insufficient permissions' });
  };
};

// Middleware to check if user has any of the specified permissions
export const hasAnyPermission = (permissions: string[]) => {
  return (req: Request, res: Response, next: NextFunction) => {
    const userContext = getUserContext(req);

    if (!req.user) {
      logger.warn('Access denied: Authentication required', {
        ...userContext,
        reason: 'no_authentication',
        requiredPermissions: permissions,
      });
      return res.status(401).json({ message: 'Authentication required' });
    }

    const userPermissions = getAllPermissionsForUser(req.user);

    if (permissions.some(permission => userPermissions.includes(permission))) {
      return next();
    }

    logger.warn('Access denied: User lacks any of the required permissions', {
      ...userContext,
      reason: 'insufficient_permissions_any',
      requiredPermissions: permissions,
      userPermissions: userPermissions,
    });
    return res.status(403).json({ message: 'Forbidden: Insufficient permissions' });
  };
};

// Middleware to check if user has all of the specified permissions
export const hasAllPermissions = (permissions: string[]) => {
  return (req: Request, res: Response, next: NextFunction) => {
    const userContext = getUserContext(req);

    if (!req.user) {
      logger.warn('Access denied: Authentication required', {
        ...userContext,
        reason: 'no_authentication',
        requiredPermissions: permissions,
      });
      return res.status(401).json({ message: 'Authentication required' });
    }

    const userPermissions = getAllPermissionsForUser(req.user);

    if (permissions.every(permission => userPermissions.includes(permission))) {
      return next();
    }

    const missingPermissions = permissions.filter(permission => !userPermissions.includes(permission));

    logger.warn('Access denied: User lacks some required permissions', {
      ...userContext,
      reason: 'insufficient_permissions_all',
      requiredPermissions: permissions,
      userPermissions: userPermissions,
      missingPermissions: missingPermissions,
    });
    return res.status(403).json({ message: 'Forbidden: Insufficient permissions' });
  };
};
