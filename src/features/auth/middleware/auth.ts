import { Request, Response, NextFunction } from 'express';
import jwt from 'jsonwebtoken';
import { logger } from '../../../shared/lib/logger';

// Add user property to Express Request
declare global {
  namespace Express {
    interface Request {
      user?: any;
    }
  }
}

// Helper function to extract request context for logging
const getRequestContext = (req: Request) => {
  return {
    ip: req.ip || req.connection?.remoteAddress || 'unknown',
    userAgent: req.get('User-Agent') || 'unknown',
    route: req.originalUrl || req.url,
    method: req.method,
  };
};

// Generate JWT token
export const generateToken = (user: any) => {
  return jwt.sign(
    {
      id: user.id,
      email: user.email,
      roles: user.roles || ['user'], // Default to 'user' role if none specified
    },
    process.env.JWT_SECRET || 'your-secret-key',
    { expiresIn: '7d' }
  );
};

// Verify JWT token middleware
export const verifyToken = (req: Request, res: Response, next: NextFunction) => {
  const requestContext = getRequestContext(req);
  const token = req.headers.authorization?.split(' ')[1];

  if (!token) {
    logger.warn('Authentication failed: No token provided', {
      ...requestContext,
      reason: 'no_token',
    });
    return res.status(401).json({ message: 'No token provided' });
  }

  try {
    const decoded = jwt.verify(token, process.env.JWT_SECRET || 'your-secret-key');
    req.user = decoded;
    next();
  } catch (error) {
    logger.warn('Authentication failed: Invalid token', {
      ...requestContext,
      reason: 'invalid_token',
      error: error instanceof Error ? error.message : 'Unknown error',
    });
    return res.status(401).json({ message: 'Invalid token' });
  }
};

// Require authentication middleware
export const requireAuth = (req: Request, res: Response, next: NextFunction) => {
  const requestContext = getRequestContext(req);

  if (!req.user) {
    logger.warn('Access denied: Authentication required', {
      ...requestContext,
      reason: 'authentication_required',
    });
    return res.status(401).json({ message: 'Authentication required' });
  }
  next();
};
