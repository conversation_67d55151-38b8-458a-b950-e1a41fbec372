import { Request, Response, NextFunction } from 'express';
import { competitionCache } from '../../competitions/services/competition-cache';
import { queueCleanupService } from '../../competitions/services/queue-cleanup-service';
import { WebSocketServer } from '../../websocket/server';
import { competitionAggregatorService } from '../../competitions/services/competition-aggregator';

export const getServerStatus = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    // Get WebSocket server instance
    const wsServer = WebSocketServer.getInstance();

    // Get all cached competitions
    const cachedCompetitions = competitionCache.getAllCachedCompetitions();

    // Build status object
    const status = {
      cache: {
        competitionCount: cachedCompetitions.length,
        competitions: cachedCompetitions.map(comp => ({
          id: comp.id,
          cachedSince: comp.cachedAt,
          messageCount: competitionCache.getMessages(comp.id).length,
          queueStats: competitionCache.getQueueStats(comp.id),
        })),
      },
      sockets: {
        totalConnections: wsServer.getConnectedClientsCount(),
        rooms: wsServer.getRoomStats(),
      },
      queue: competitionCache.getQueueMetrics(),
    };

    res.json({ data: status });
  } catch (error) {
    next(error);
  }
};

export const refreshCompetitionCache = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const competitionId = parseInt(req.params.id, 10);

    if (isNaN(competitionId)) {
      res.status(400).json({ message: 'Invalid competition ID' });
      return;
    }

    // Invalidate the cache for this competition
    competitionCache.invalidate(competitionId);

    // Fetch fresh data and store it in the cache
    const refreshedData = await competitionAggregatorService.getCompetitionOnTheDay(competitionId);

    if (!refreshedData) {
      res.status(404).json({ message: 'Competition not found' });
      return;
    }

    res.json({
      message: `Competition ${competitionId} cache refreshed successfully`,
      data: {
        id: competitionId,
        cachedAt: new Date(),
        messageCount: competitionCache.getMessages(competitionId).length,
      },
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Get global queue metrics for monitoring
 */
export const getQueueMetrics = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const metrics = competitionCache.getQueueMetrics();
    res.json({ data: metrics });
  } catch (error) {
    next(error);
  }
};

/**
 * Get queue statistics for a specific competition
 */
export const getQueueStats = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const competitionId = parseInt(req.params.competitionId, 10);

    if (isNaN(competitionId)) {
      res.status(400).json({ message: 'Invalid competition ID' });
      return;
    }

    const stats = competitionCache.getQueueStats(competitionId);
    res.json({
      data: {
        competitionId,
        ...stats,
      },
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Perform global queue cleanup (remove expired messages)
 */
export const performQueueCleanup = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const cleanupResult = competitionCache.performGlobalCleanup();
    res.json({
      message: 'Queue cleanup completed successfully',
      data: cleanupResult,
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Get queue cleanup service status
 */
export const getQueueServiceStatus = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const serviceStatus = queueCleanupService.getStatus();
    res.json({
      data: serviceStatus,
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Get comprehensive health status of the server
 */
export const getHealthStatus = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const { healthMonitor } = await import('../../../shared/lib/health-monitor');
    const { restartManager } = await import('../../../shared/lib/restart-manager');
    const { dbConnect } = await import('../../../shared/lib/db');
    const { getDatabaseConnectionState, getWordPressConnectionState } = await import(
      '../../../index'
    );

    // Quick database check
    let dbStatus = 'unknown';
    let dbConnectionState = 'unknown';

    try {
      dbConnectionState = getDatabaseConnectionState() ? 'connected' : 'disconnected';
      if (getDatabaseConnectionState()) {
        await dbConnect.testConnection();
        dbStatus = 'connected';
      } else {
        dbStatus = 'disconnected';
      }
    } catch (error) {
      dbStatus = 'disconnected';
      dbConnectionState = 'disconnected';
    }

    // Quick WordPress check
    let wpStatus = 'unknown';
    let wpConnectionState = 'unknown';

    try {
      wpConnectionState = getWordPressConnectionState() ? 'connected' : 'disconnected';
      if (getWordPressConnectionState()) {
        wpStatus = 'connected';
      } else {
        wpStatus = 'disconnected';
      }
    } catch (error) {
      wpStatus = 'disconnected';
      wpConnectionState = 'disconnected';
    }

    const overallStatus = dbStatus === 'connected' && wpStatus === 'connected' ? 'ok' : 'degraded';

    const healthStatus = {
      status: overallStatus,
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      memory: process.memoryUsage(),
      database: {
        status: dbStatus,
        connectionState: dbConnectionState,
        message:
          dbStatus === 'connected'
            ? 'Database is available'
            : 'Database is unavailable - running in degraded mode',
      },
      wordpress: {
        status: wpStatus,
        connectionState: wpConnectionState,
        message:
          wpStatus === 'connected'
            ? 'WordPress is available'
            : 'WordPress is unavailable - WordPress features disabled',
      },
      healthMonitor: healthMonitor.getStatus(),
      restartManager: restartManager.getStats(),
      version: process.version,
      pid: process.pid,
    };

    res.status(200).json(healthStatus);
  } catch (error) {
    res.status(500).json({
      status: 'error',
      message: 'Health check failed',
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString(),
    });
  }
};
