import express from 'express';
import {
  getServerStatus,
  getQueueMetrics,
  getQueueStats,
  performQueueCleanup,
  getQueueServiceStatus,
  getHealthStatus,
} from '../controllers';
import { WebSocketServer } from '../../websocket';
import { competitionCache } from '../../competitions/services/competition-cache';
import { competitionAggregatorService } from '../../competitions/services/competition-aggregator';
import { requireAuth, verifyToken } from '../../auth/middleware/auth';
import { isAdmin, hasPermission } from '../../auth/middleware/rbac';
import { PERMISSIONS } from '../../auth/config/roles';

const adminRouter = express.Router();

// Add debugging middleware
adminRouter.use((req, res, next) => {
  console.log(`ADMIN ROUTER: ${req.method} ${req.originalUrl} (${req.url})`);
  console.log('Authorization header:', req.headers.authorization);
  next();
});

// Add the middleware to protect routes
adminRouter.use(verifyToken); // First verify and extract the JWT token
adminRouter.use(requireAuth); // Then check if user exists
adminRouter.use(isAdmin); // Finally check if user has admin role

// Health check endpoint
adminRouter.get('/health', getHealthStatus);

// Route for server status
adminRouter.get('/status', getServerStatus);

// Queue monitoring routes
adminRouter.get('/queue/metrics', getQueueMetrics);
adminRouter.get('/queue/stats/:competitionId', getQueueStats);
adminRouter.get('/queue/service-status', getQueueServiceStatus);
adminRouter.post('/queue/cleanup', performQueueCleanup);

// Example of using permission-based access control
adminRouter.get('/users', hasPermission(PERMISSIONS.MANAGE_USERS), (req, res) => {
  // Get users logic here
  res.json({ message: 'Users list' });
});

/**
 * Refreshes the cache for a specific competition.
 * ...and sends a message to all clients in the competition room.
 * So...use sparingly!!!
 */
adminRouter.post('/competitions/:id/refresh-cache', (req, res) => {
  const competitionId = parseInt(req.params.id, 10);

  if (isNaN(competitionId)) {
    return res.status(400).json({ message: 'Invalid competition ID' });
  }

  try {
    // Get WebSocket server instance
    const wsServer = WebSocketServer.getInstance();

    // Invalidate the cache for this competition
    competitionCache.invalidate(competitionId);

    // Fetch fresh data
    competitionAggregatorService
      .getCompetitionOnTheDay(competitionId)
      .then(competitionData => {
        // Notify clients in the competition room
        const roomName = `competition-${competitionId}`;
        wsServer.io.to(roomName).emit('cache-updated', {
          comp: { id: competitionId },
          action: 'cache-updated',
          payload: competitionData,
        });

        // Return success response
        res.json({
          message: `Competition ${competitionId} cache refreshed successfully`,
          data: {
            id: competitionId,
            cachedAt: new Date(),
            messageCount: competitionCache.getMessages(competitionId).length,
          },
        });
      })
      .catch((error: Error) => {
        console.error(`Error refreshing competition ${competitionId}:`, error);
        res.status(500).json({
          message: `Error refreshing competition ${competitionId}`,
          error: error.message,
        });
      });
  } catch (error: any) {
    console.error(`Error in refresh cache endpoint:`, error);
    res.status(500).json({
      message: 'Internal server error',
      error: error.message,
    });
  }
});

// Add a test route
adminRouter.get('/test', (req, res) => {
  res.json({ message: 'Admin router test route is working!' });
});

export { adminRouter };
