// Define constants for message action types
import { a } from 'vitest/dist/suite-dWqIFb_-';

export const ExternalMessageActionNotInterestedIn = {
  ping: 'ping',
  e4s_init: 'e4s_init',
} as const;

// not yet coded for messages
export const ExternalMessageActionNotYetCoded = {
  eg_options: 'eg_options',
  'entries-checkin': 'entries-checkin',
} as const;

// These will not necessarily be replayed in the client, but still messages will display on the live feed
export const ExternalMessageActionInterestedIn = {
  refreshreq: 'refreshreq',
  'result-update': 'result-update',
  photofinish: 'photofinish',
  'field-results': 'field-results',
  'results-manual': 'results-manual',
  'field-nextup': 'field-nextup',
} as const;

// Create union types from the constant values
export type ExternalMessageActionNotInterestedInType =
  (typeof ExternalMessageActionNotInterestedIn)[keyof typeof ExternalMessageActionNotInterestedIn];

export type ExternalMessageActionNotYetCodedType =
  (typeof ExternalMessageActionNotYetCoded)[keyof typeof ExternalMessageActionNotYetCoded];

export type ExternalMessageActionInterestedInType =
  (typeof ExternalMessageActionInterestedIn)[keyof typeof ExternalMessageActionInterestedIn];

// Main union type for external message actions
export type ExternalMessageAction =
  | ExternalMessageActionNotInterestedInType
  | ExternalMessageActionNotYetCodedType
  | ExternalMessageActionInterestedInType;
export type LiteServerMessageAction = 'cache-updated';
export type ClientMessageAction = 'subscribe' | 'unsubscribe' | 'ping';

export interface ExternalSocketMessage {
  key: string;
  comp: {
    id: number;
  };
  action: ExternalMessageAction;
  deviceKey: number;
  securityKey: string;
  domain: string;
  payload: unknown;
  utcTime: string;
}

export interface LiteServerSocketMessage {
  comp: {
    id: number;
  };
  action: LiteServerMessageAction;
  payload: unknown;
}

// New interface for incoming client messages
export interface ClientSocketMessage {
  comp: {
    id: number;
  };
  action: ClientMessageAction;
  payload: unknown;
}

// Specific client message types
export interface PingMessage extends ExternalSocketMessage {
  action: 'ping';
  payload: {
    ping: boolean;
  };
}

export interface CompetitionSubscribeMessage extends ClientSocketMessage {
  action: 'subscribe';
  payload: {
    compId: number;
  };
}

// Type guard functions to check message types
export function isCompetitionUpdate(message: ExternalSocketMessage): boolean {
  // Is there a comp id o this message? and log out what it is.
  // console.log('isCompetitionUpdate called', message);
  // console.log('isCompetitionUpdate called', message.comp);
  // console.log('isCompetitionUpdate called', message.comp.id);

  // TODO limit here the message.action to the ones we want.
  // At the moment we're just sending up all messages for the competition.
  return message && message.comp && typeof message.comp.id === 'number';
}

// Type guards for client messages
// export function isPingMessage(message: ClientSocketMessage): message is PingMessage {
//   return message.action === 'ping';
// }

export function isCompetitionSubscribe(
  message: ClientSocketMessage
): message is CompetitionSubscribeMessage {
  console.warn('isCompetitionSubscribe called', message);
  return message.action === 'subscribe';
}

export function isNotInterestedExternalAction(action: ExternalMessageAction): boolean {
  if (typeof action === 'undefined' || !action) {
    console.log('!!!!!! isNotInterestedExternalAction called with non-string action', action);
    return false;
  }

  return Object.values(ExternalMessageActionNotInterestedIn).includes(
    action as ExternalMessageActionNotInterestedInType
  );
}

export function isInterestedExternalAction(action: ExternalMessageAction): boolean {
  return Object.values(ExternalMessageActionInterestedIn).includes(
    action as ExternalMessageActionInterestedInType
  );
}
