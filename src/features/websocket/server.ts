import WebSocket from 'ws';
import { Server, Socket } from 'socket.io';
import { logger } from '../../shared/lib/logger';
import { IncomingMessage, Server as HttpServer } from 'http';
import {
  isCompetitionUpdate,
  ClientSocketMessage,
  isCompetitionSubscribe,
  ExternalSocketMessage,
  LiteServerSocketMessage,
  isNotInterestedExternalAction,
} from './types';
import { competitionCache } from '../../features/competitions/services/competition-cache';
import { competitionAggregatorService } from '../../features/competitions/services/competition-aggregator';
import { domainValidator } from '../../shared/lib/domain-validator';

export class WebSocketServer {
  private externalWs: WebSocket | null = null;
  private connectedClients: number = 0;
  private reconnectTimer: NodeJS.Timeout | null = null;
  private readonly externalWsUrl: string;
  // Message queue to store messages by competition ID
  private messageQueues: Map<number, ExternalSocketMessage[]> = new Map();
  private static instance: WebSocketServer | null = null;

  public io: Server;

  public static getInstance(): WebSocketServer {
    if (!WebSocketServer.instance) {
      throw new Error('WebSocketServer has not been initialized');
    }
    return WebSocketServer.instance;
  }

  constructor(httpServer: HttpServer, externalWsUrl: string) {
    this.externalWsUrl = externalWsUrl;

    // Create Socket.IO server with simplified configuration
    this.io = new Server(httpServer, {
      cors: {
        origin: '*',
        methods: ['GET', 'POST'],
        credentials: true,
      },
      transports: ['websocket', 'polling'],
    });
    // path: '/custom/socket.io',

    // Set the static instance
    WebSocketServer.instance = this;

    // Log Socket.IO server configuration
    logger.info(
      `Socket.IO server configuration: ${JSON.stringify({
        path: this.io.path(),
        transports: this.io.engine.opts.transports,
      })}`
    );

    this.setupSocketIO();
    this.connectToExternalWS();
  }

  private setupSocketIO(): void {
    this.io.on('connection', (socket: Socket) => {
      this.connectedClients++;
      logger.info(`Client connected. ID: ${socket.id}, Total clients: ${this.connectedClients}`);
      logger.info(`Client transport: ${socket.conn.transport.name}`);
      logger.info(`Client IP: ${socket.handshake.address}`);

      // Log socket.io engine details
      logger.info(
        `Socket.IO engine details: ${JSON.stringify({
          pingTimeout: this.io.engine.opts.pingTimeout,
          pingInterval: this.io.engine.opts.pingInterval,
          upgradeTimeout: this.io.engine.opts.upgradeTimeout,
          maxHttpBufferSize: this.io.engine.opts.maxHttpBufferSize,
        })}`
      );

      // Monitor socket events
      socket.conn.on('packet', packet => {
        if (packet.type === 'ping') {
          logger.debug(`Ping received from client ${socket.id}`);
        }
      });

      socket.conn.on('packetCreate', packet => {
        if (packet.type === 'pong') {
          logger.debug(`Pong sent to client ${socket.id}`);
        }
      });

      socket.conn.on('upgrade', transport => {
        logger.info(`Client ${socket.id} transport upgraded to ${transport.name}`);
      });

      socket.conn.on('error', err => {
        logger.error(`Socket connection error for client ${socket.id}:`, err);
      });

      // Handle client joining a competition room
      socket.on('join-competition', (competitionId: number) => {
        const roomName = `competition-${competitionId}`;
        socket.join(roomName);
        logger.info(`Client ${socket.id} joined room: ${roomName}`);
      });

      // Handle client leaving a competition room
      socket.on('leave-competition', (competitionId: number) => {
        const roomName = `competition-${competitionId}`;
        socket.leave(roomName);
        logger.info(`Client ${socket.id} left room: ${roomName}`);
      });

      // Handle client messages with the new format
      socket.on('message', (data: string) => {
        try {
          const message = JSON.parse(data) as ClientSocketMessage;
          this.handleClientMessage(socket, message);
        } catch (error) {
          logger.error('Error parsing client message:', error);
        }
      });

      socket.on('disconnect', reason => {
        this.connectedClients--;
        logger.info(
          `Client ${socket.id} disconnected. Reason: ${reason}. Total clients: ${this.connectedClients}`
        );

        // Log additional details about the disconnection
        logger.info(
          `Disconnect details - Socket ID: ${socket.id}, Transport: ${socket.conn.transport?.name || 'unknown'}`
        );

        // Check if the socket was in any rooms before disconnecting
        const rooms = Array.from(socket.rooms || []);
        logger.info(`Socket ${socket.id} was in rooms: ${JSON.stringify(rooms)}`);
      });
    });
  }

  /**
   * Handles client messages with the new format.
   * @param socket
   * @param message
   * @private
   */
  private handleClientMessage(socket: Socket, message: ClientSocketMessage): void {
    try {
      logger.info(`Received client message: ${message.action}`);

      // Handle competition subscription
      if (isCompetitionSubscribe(message)) {
        try {
          const compId = message.payload.compId || message.comp.id;
          const roomName = `competition-${compId}`;
          socket.join(roomName);

          // Log out how many users are connected to this room.
          const clientsInRoom = this.io.sockets.adapter.rooms.get(roomName)?.size || 0;

          logger.info(
            `Client ${socket.id} subscribed to competition: ${compId}. ` +
              `Clients in room: ${clientsInRoom}`
          );

          // Send confirmation
          socket.emit('subscribed', {
            comp: {
              id: compId,
            },
            status: 'success',
          });

          // Send any queued messages for this competition
          const queuedMessages = competitionCache.getMessages(compId);
          if (queuedMessages.length > 0) {
            logger.info(`Sending ${queuedMessages.length} queued messages to client ${socket.id}`);
            queuedMessages.forEach(queuedMessage => {
              socket.emit('competition-update', queuedMessage);
            });
          }
        } catch (error) {
          logger.error(`Error handling subscription for client ${socket.id}:`, error);
        }
        return;
      }

      // Handle other message types based on action
      switch (message.action) {
        case 'unsubscribe':
          if (message.comp?.id) {
            const roomName = `competition-${message.comp.id}`;
            socket.leave(roomName);
            logger.info(`Client ${socket.id} unsubscribed from competition: ${message.comp.id}`);
            socket.emit('unsubscribed', {
              competitionId: message.comp.id,
              status: 'success',
            });
          }
          break;

        case 'ping':
          // Handle ping messages
          logger.info(`Received ping from client ${socket.id}`);
          socket.emit('pong', {
            status: 'success',
            timestamp: new Date().toISOString(),
          });
          break;

        default:
          logger.info(`Unhandled client message action: ${message.action}`);
          break;
      }
    } catch (error) {
      logger.error(`Error in handleClientMessage for client ${socket.id}:`, error);
    }
  }

  /**
   * Connects to the external WebSocket service.
   * @private
   */
  private connectToExternalWS(): void {
    logger.info(`Connecting to external WebSocket: ${this.externalWsUrl}`);

    this.externalWs = new WebSocket(this.externalWsUrl);

    this.externalWs.on('open', () => {
      logger.info('Connected to external WebSocket service');
      if (this.reconnectTimer) {
        clearTimeout(this.reconnectTimer);
        this.reconnectTimer = null;
      }
    });

    this.externalWs.on('message', (data: WebSocket.RawData) => {
      //console.log('message data: ', data.toString());

      try {
        const message: ExternalSocketMessage = JSON.parse(data.toString());

        logger.info(`>>> Received message from external service, action: ` + message.action);

        // Check if the domain is allowed before processing the message
        if (!domainValidator.isAllowed(message.domain)) {
          logger.info(`Message from domain '${message.domain}' rejected - not in allowed domains list`);
          return;
        }

        //  If action is paying return message of pong To the external server
        // if (message.action === 'ping') {
        //   logger.info(`<<< Sending pong to external service`);
        //   this.externalWs?.send(JSON.stringify({ action: 'pong', payload: { pong: true } }));
        //   return;
        // }

        //  There are going to be a load of messages that need to get though here.
        // but for now, just filter out the ones we don't want.
        if (isNotInterestedExternalAction(message.action)) {
          // logger.info(`Not interested in message: ${message.action}`);
          return;
        }

        const isMessageToCompetition = isCompetitionUpdate(message);
        console.log('isMessageToCompetition: ' + isMessageToCompetition);

        // Emit to specific competition room if it's a competition update
        if (isCompetitionUpdate(message)) {
          console.log('isCompetitionUpdate BROADCAST', message);
          const competitionId: number = message.comp.id;
          const roomName = `competition-${competitionId}`;

          // Message from server does not have a display time stick the UTC time on it
          if (!message.utcTime) {
            message.utcTime = new Date().toISOString();
          }

          // Store the message in the queue using competitionCache
          competitionCache.storeMessage(competitionId, message);

          // Emit to the specific competition room
          this.io.to(roomName).emit('competition-update', message);

          // Log out how many users are connected to this room
          const clientsInRoom = this.io.sockets.adapter.rooms.get(roomName)?.size || 0;
          logger.info(`Clients in ${roomName}: ${clientsInRoom}`);

          logger.info(`Sent competition update to ${clientsInRoom} clients in room: ${roomName}`);

          // Check if it's a cache invalidation message
          if (message.action === 'refreshreq') {
            const competitionId = message.comp.id;
            logger.info(`Received cache invalidation for competition: ${competitionId}`);

            // Invalidate the cache for this competition
            competitionCache.invalidate(competitionId);
            // Note: clearMessageQueue is now called inside invalidate()

            // Notify clients in the competition room with the updated data
            const roomName = `competition-${competitionId}`;
            competitionAggregatorService
              .getCompetitionOnTheDay(competitionId)
              .then(competitionData => {
                const messageForClients: LiteServerSocketMessage = {
                  comp: {
                    id: competitionId,
                  },
                  action: 'cache-updated',
                  payload: competitionData,
                };

                this.io.to(roomName).emit('cache-updated', messageForClients);
                logger.info(`Sent updated competition data to room: ${roomName}`);
              })
              .catch((err: Error) =>
                logger.error(`Failed to send updated competition data: ${err.message}`)
              );
          }
        } else {
          // Log the message but don't broadcast it
          logger.info(
            `Received message with no specific room target (comp ID missing). Not broadcasting.`,
            message
          );
          logger.debug(`Unrouted message content: ${JSON.stringify(message)}`);
        }
      } catch (error) {
        logger.error('Error parsing WebSocket message:', error);
        // Don't broadcast raw messages either
        logger.debug(`Unparseable message content: ${data.toString()}`);
      }
    });

    this.externalWs.on('error', (error: Error) => {
      try {
        logger.error('External WebSocket error:', error);
      } catch (logError) {
        // NO console output - use emergency logging to prevent EPIPE errors
        try {
          logger.emergency('Failed to log WebSocket error', {
            logError: (logError as any).message,
            originalError: error.message
          });
        } catch {
          // If everything fails, silently continue to prevent crashes
        }
      }
    });

    this.externalWs.on('close', (code: number, reason: Buffer) => {
      logger.warn(`External WebSocket connection closed with code ${code}: ${reason.toString()}`);
      this.scheduleReconnect();
    });
  }

  private scheduleReconnect(): void {
    if (!this.reconnectTimer) {
      this.reconnectTimer = setTimeout(() => {
        logger.info('Attempting to reconnect to external WebSocket');
        this.connectToExternalWS();
      }, 5000); // Reconnect after 5 seconds
    }
  }

  // Remove the old message queue methods as they're now in competitionCache

  /**
   * Get the number of connected clients
   */
  public getConnectedClientsCount(): number {
    return this.connectedClients;
  }

  /**
   * Get statistics for all competition rooms
   */
  public getRoomStats(): Record<string, number> {
    const roomStats: Record<string, number> = {};

    // Get all rooms that match the competition pattern
    this.io.sockets.adapter.rooms.forEach((_, roomName) => {
      if (roomName.startsWith('competition-')) {
        const clientsInRoom = this.io.sockets.adapter.rooms.get(roomName)?.size || 0;
        roomStats[roomName] = clientsInRoom;
      }
    });

    return roomStats;
  }
}
