import { describe, it, expect } from 'vitest';
import {
  isInterestedExternalAction,
  ExternalMessageActionInterestedIn,
  ExternalMessageAction,
} from '../types';

describe('websocket/types', () => {
  describe('isInterestedExternalAction', () => {
    it('should return true for actions we are interested in', () => {
      // Test each interested action
      Object.values(ExternalMessageActionInterestedIn).forEach(action => {
        expect(isInterestedExternalAction(action as ExternalMessageAction)).toBe(true);
      });
    });

    it('should return false for actions we are not interested in', () => {
      // Test some actions we're not interested in
      const notInterestedActions = [
        'ping',
        'e4s_init',
        'eg_options',
        'entries-checkin',
        'unknown-action',
      ] as ExternalMessageAction[];

      notInterestedActions.forEach(action => {
        expect(isInterestedExternalAction(action)).toBe(false);
      });
    });
  });
});
