import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { domainValidator } from '../../../shared/lib/domain-validator';
import { ExternalSocketMessage } from '../types';

describe('WebSocket Domain Filtering', () => {
  let originalEnv: string | undefined;

  beforeEach(() => {
    // Store original environment variable
    originalEnv = process.env.ALLOWED_DOMAINS;
    
    // Mock logger to avoid console output during tests
    vi.mock('../../../shared/lib/logger', () => ({
      logger: {
        info: vi.fn(),
        warn: vi.fn(),
        error: vi.fn(),
        debug: vi.fn(),
      },
    }));
  });

  afterEach(() => {
    // Restore original environment variable
    if (originalEnv !== undefined) {
      process.env.ALLOWED_DOMAINS = originalEnv;
    } else {
      delete process.env.ALLOWED_DOMAINS;
    }
    
    // Reload domain validator to pick up environment changes
    domainValidator.reload();
    
    vi.clearAllMocks();
  });

  const createTestMessage = (domain: string): ExternalSocketMessage => ({
    key: '0',
    comp: { id: 706 },
    action: 'field-nextup',
    deviceKey: 0.6172290287363388,
    securityKey: '',
    domain,
    payload: {
      athlete: { id: 193681, name: 'Taejan BLAKE', attempt: 1.65 },
      comp: { id: 706, name: 'English Schools 2025' },
      event: { id: 16660, name: 'High Jump IB', heatNo: 1 }
    },
    utcTime: new Date().toISOString()
  });

  describe('Domain validation in production environment', () => {
    it('should allow messages from production domain only', () => {
      process.env.ALLOWED_DOMAINS = 'entry4sports.co.uk';
      domainValidator.reload();

      const productionMessage = createTestMessage('entry4sports.co.uk');
      const uatMessage = createTestMessage('uat.entry4sports.co.uk');
      const maliciousMessage = createTestMessage('malicious.com');

      expect(domainValidator.isAllowed(productionMessage.domain)).toBe(true);
      expect(domainValidator.isAllowed(uatMessage.domain)).toBe(false);
      expect(domainValidator.isAllowed(maliciousMessage.domain)).toBe(false);
    });
  });

  describe('Domain validation in development environment', () => {
    it('should allow messages from both production and UAT domains', () => {
      process.env.ALLOWED_DOMAINS = 'entry4sports.co.uk,uat.entry4sports.co.uk';
      domainValidator.reload();

      const productionMessage = createTestMessage('entry4sports.co.uk');
      const uatMessage = createTestMessage('uat.entry4sports.co.uk');
      const maliciousMessage = createTestMessage('malicious.com');

      expect(domainValidator.isAllowed(productionMessage.domain)).toBe(true);
      expect(domainValidator.isAllowed(uatMessage.domain)).toBe(true);
      expect(domainValidator.isAllowed(maliciousMessage.domain)).toBe(false);
    });
  });

  describe('Domain validation edge cases', () => {
    it('should handle case insensitive domain matching', () => {
      process.env.ALLOWED_DOMAINS = 'entry4sports.co.uk';
      domainValidator.reload();

      const upperCaseMessage = createTestMessage('ENTRY4SPORTS.CO.UK');
      const mixedCaseMessage = createTestMessage('Entry4Sports.co.uk');

      expect(domainValidator.isAllowed(upperCaseMessage.domain)).toBe(true);
      expect(domainValidator.isAllowed(mixedCaseMessage.domain)).toBe(true);
    });

    it('should handle domains with whitespace', () => {
      process.env.ALLOWED_DOMAINS = 'entry4sports.co.uk';
      domainValidator.reload();

      const messageWithSpaces = createTestMessage(' entry4sports.co.uk ');
      const messageWithTabs = createTestMessage('\tentry4sports.co.uk\n');

      expect(domainValidator.isAllowed(messageWithSpaces.domain)).toBe(true);
      expect(domainValidator.isAllowed(messageWithTabs.domain)).toBe(true);
    });

    it('should reject empty or invalid domains', () => {
      process.env.ALLOWED_DOMAINS = 'entry4sports.co.uk';
      domainValidator.reload();

      const emptyDomainMessage = createTestMessage('');
      const whitespaceDomainMessage = createTestMessage('   ');

      expect(domainValidator.isAllowed(emptyDomainMessage.domain)).toBe(false);
      expect(domainValidator.isAllowed(whitespaceDomainMessage.domain)).toBe(false);
    });

    it('should allow all domains when no configuration is set', () => {
      delete process.env.ALLOWED_DOMAINS;
      domainValidator.reload();

      const productionMessage = createTestMessage('entry4sports.co.uk');
      const uatMessage = createTestMessage('uat.entry4sports.co.uk');
      const maliciousMessage = createTestMessage('malicious.com');

      expect(domainValidator.isAllowed(productionMessage.domain)).toBe(true);
      expect(domainValidator.isAllowed(uatMessage.domain)).toBe(true);
      expect(domainValidator.isAllowed(maliciousMessage.domain)).toBe(true);
    });
  });

  describe('Message filtering scenarios', () => {
    it('should simulate production filtering scenario', () => {
      // Simulate production environment
      process.env.ALLOWED_DOMAINS = 'entry4sports.co.uk';
      domainValidator.reload();

      // Test messages from different domains
      const testCases = [
        { domain: 'entry4sports.co.uk', shouldAllow: true, description: 'production domain' },
        { domain: 'uat.entry4sports.co.uk', shouldAllow: false, description: 'UAT domain' },
        { domain: 'staging.entry4sports.co.uk', shouldAllow: false, description: 'staging domain' },
        { domain: 'malicious.com', shouldAllow: false, description: 'malicious domain' },
        { domain: 'entry4sports.com', shouldAllow: false, description: 'similar but different domain' },
      ];

      testCases.forEach(({ domain, shouldAllow, description }) => {
        const message = createTestMessage(domain);
        expect(domainValidator.isAllowed(message.domain)).toBe(shouldAllow);
      });
    });

    it('should simulate development filtering scenario', () => {
      // Simulate development environment
      process.env.ALLOWED_DOMAINS = 'entry4sports.co.uk,uat.entry4sports.co.uk';
      domainValidator.reload();

      // Test messages from different domains
      const testCases = [
        { domain: 'entry4sports.co.uk', shouldAllow: true, description: 'production domain' },
        { domain: 'uat.entry4sports.co.uk', shouldAllow: true, description: 'UAT domain' },
        { domain: 'staging.entry4sports.co.uk', shouldAllow: false, description: 'staging domain' },
        { domain: 'malicious.com', shouldAllow: false, description: 'malicious domain' },
      ];

      testCases.forEach(({ domain, shouldAllow, description }) => {
        const message = createTestMessage(domain);
        expect(domainValidator.isAllowed(message.domain)).toBe(shouldAllow);
      });
    });
  });
});
