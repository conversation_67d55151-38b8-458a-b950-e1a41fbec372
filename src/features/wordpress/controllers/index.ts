import { Request, Response, NextFunction } from 'express';
import { wpAuthService } from '../services/wp-auth';
import { logger } from '../../../shared/lib/logger';

export const getWordPressData = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const { endpoint } = req.params;
    const queryParams = new URLSearchParams(req.query as Record<string, string>);

    const wpApiUrl = `${process.env.WP_API_BASE_URL}/${endpoint}${queryParams.toString() ? `?${queryParams}` : ''}`;

    const response = await wpAuthService.makeAuthenticatedRequest(wpApiUrl, {
      method: 'GET',
    });

    res.json(response.data);
  } catch (error) {
    logger.error('WordPress GET request failed:', error);
    next(error);
  }
};

export const postWordPressData = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const { endpoint } = req.params;
    const wpApiUrl = `${process.env.WP_API_BASE_URL}/${endpoint}`;

    const response = await wpAuthService.makeAuthenticatedRequest(wpApiUrl, {
      method: 'POST',
      data: req.body,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    res.status(201).json(response.data);
  } catch (error) {
    logger.error('WordPress POST request failed:', error);
    next(error);
  }
};

export const updateWordPressData = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const { endpoint, id } = req.params;
    const wpApiUrl = `${process.env.WP_API_BASE_URL}/${endpoint}/${id}`;

    const response = await wpAuthService.makeAuthenticatedRequest(wpApiUrl, {
      method: 'PUT',
      data: req.body,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    res.json(response.data);
  } catch (error) {
    logger.error('WordPress PUT request failed:', error);
    next(error);
  }
};

export const deleteWordPressData = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const { endpoint, id } = req.params;
    const wpApiUrl = `${process.env.WP_API_BASE_URL}/${endpoint}/${id}`;

    const response = await wpAuthService.makeAuthenticatedRequest(wpApiUrl, {
      method: 'DELETE',
    });

    res.json(response.data);
  } catch (error) {
    logger.error('WordPress DELETE request failed:', error);
    next(error);
  }
};

export const testWordPressConnection = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    await wpAuthService.authenticate();

    // Test with a simple API call
    const response = await wpAuthService.makeAuthenticatedRequest(
      `${process.env.WP_API_BASE_URL}/users/me`
    );

    res.json({
      success: true,
      message: 'WordPress connection successful',
      user: response.data,
    });
  } catch (error: any) {
    // Log detailed error information
    if (error.response) {
      // HTTP error response from WordPress
      logger.error('WordPress connection test failed - HTTP error:', {
        status: error.response.status,
        statusText: error.response.statusText,
        data: error.response.data,
        headers: error.response.headers,
        url: `${process.env.WP_API_BASE_URL}/users/me`,
      });
    } else if (error.request) {
      // Network error - request was made but no response received
      logger.error('WordPress connection test failed - Network error:', {
        message: error.message,
        code: error.code,
        url: `${process.env.WP_API_BASE_URL}/users/me`,
      });
    } else {
      // Other error (authentication, configuration, etc.)
      logger.error('WordPress connection test failed - General error:', {
        message: error.message,
        stack: error.stack,
      });
    }

    res.status(500).json({
      success: false,
      message: 'WordPress connection failed',
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
};
