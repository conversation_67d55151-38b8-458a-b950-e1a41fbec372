import express from 'express';
import {
  getWordPressData,
  postWordPressData,
  updateWordPressData,
  deleteWordPressData,
  testWordPressConnection,
} from '../controllers';

const wordpressRouter = express.Router();

// Add debugging middleware
wordpressRouter.use((req, res, next) => {
  console.log(`WORDPRESS ROUTER: ${req.method} ${req.originalUrl} (${req.url})`);
  next();
});

// Test connection endpoint
wordpressRouter.get('/test', testWordPressConnection);

// Generic WordPress API proxy routes
wordpressRouter.get('/:endpoint', getWordPressData);
wordpressRouter.post('/:endpoint', postWordPressData);
wordpressRouter.put('/:endpoint/:id', updateWordPressData);
wordpressRouter.delete('/:endpoint/:id', deleteWordPressData);

export { wordpressRouter };