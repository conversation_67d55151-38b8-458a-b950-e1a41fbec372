import axios, { AxiosInstance } from 'axios';
import { logger } from '../../../shared/lib/logger';

export class WordPressAuthService {
  private axiosInstance: AxiosInstance;
  private authCookie: string | null = null;
  private nonce: string | null = null;
  private lastAuthTime: number = 0;
  private readonly AUTH_TIMEOUT = 24 * 60 * 60 * 1000; // 24 hours

  constructor() {
    this.axiosInstance = axios.create({
      timeout: 30000,
      withCredentials: true,
    });
  }

  async authenticate(): Promise<void> {
    // Check if WordPress connection is enabled
    const wpConnectionEnabled = process.env.WP_CONNECTION_ENABLED !== 'false';
    if (!wpConnectionEnabled) {
      logger.info('WordPress authentication skipped - WP_CONNECTION_ENABLED is false');
      return;
    }

    const now = Date.now();

    // Check if we have a valid cookie and nonce
    if (this.authCookie && this.nonce && now - this.lastAuthTime < this.AUTH_TIMEOUT) {
      return;
    }

    try {
      const payload = new URLSearchParams({
        log: process.env.WP_USERNAME!,
        pwd: process.env.WP_PASSWORD!,
        'interim-login': '1',
      });

      // Log authentication attempt details
      logger.info('WordPress authentication attempt:', {
        url: process.env.WP_LOGIN_URL,
        username: process.env.WP_USERNAME,
        hasPassword: !!process.env.WP_PASSWORD,
        passwordLength: process.env.WP_PASSWORD?.length || 0,
        payload: payload.toString(),
      });

      const response = await this.axiosInstance.post(process.env.WP_LOGIN_URL!, payload, {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        },
      });

      // Log response details
      logger.debug('WordPress authentication response:', {
        status: response.status,
        statusText: response.statusText,
        headers: response.headers,
        dataLength: response.data?.length || 0,
        dataPreview:
          typeof response.data === 'string' ? response.data.substring(0, 200) : response.data,
      });

      // Extract cookies from response
      const cookies = response.headers['set-cookie'];
      if (cookies) {
        this.authCookie = cookies.join('; ');
        this.lastAuthTime = now;

        // Now get the REST API nonce
        await this.getNonce();

        logger.info('WordPress authentication successful');

        // Log cookie details for debugging
        logger.debug('WordPress cookies received:', {
          cookieCount: cookies.length,
          cookies: cookies.map(c => c.split(';')[0]),
          fullCookieString: this.authCookie,
          hasNonce: !!this.nonce,
        });
      } else {
        logger.error('No authentication cookies received in response headers:', {
          responseHeaders: response.headers,
        });
        throw new Error('No authentication cookies received');
      }
    } catch (error: any) {
      logger.error('WordPress authentication failed:', {
        error: error instanceof Error ? error.message : String(error),
        status: error.response?.status,
        statusText: error.response?.statusText,
        responseData: error.response?.data,
        responseHeaders: error.response?.headers,
        url: process.env.WP_LOGIN_URL,
      });
      throw error;
    }
  }

  private async getNonce(): Promise<void> {
    try {
      // Get nonce from WordPress admin or a page that includes it
      const nonceResponse = await this.axiosInstance.get(
        `${process.env.WP_API_BASE_URL?.replace('/wp-json/wp/v2', '')}/wp-admin/admin-ajax.php?action=heartbeat`,
        {
          headers: {
            Cookie: this.authCookie,
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
          },
        }
      );

      // Try to extract nonce from response headers
      const wpNonce = nonceResponse.headers['x-wp-nonce'];
      if (wpNonce) {
        this.nonce = wpNonce;
        logger.debug('WordPress nonce obtained from headers:', { nonce: this.nonce });
      } else {
        // Alternative: try to get nonce from a different endpoint
        const altResponse = await this.axiosInstance.get(`${process.env.WP_API_BASE_URL}/`, {
          headers: {
            Cookie: this.authCookie,
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
          },
        });

        // Look for nonce in response headers
        const altNonce = altResponse.headers['x-wp-nonce'];
        if (altNonce) {
          this.nonce = altNonce;
          logger.debug('WordPress nonce obtained from API root:', { nonce: this.nonce });
        } else {
          logger.warn('No WordPress nonce found in response headers');
        }
      }
    } catch (error) {
      logger.warn('Failed to get WordPress nonce:', error);
      // Continue without nonce - some setups don't require it
    }
  }

  async makeAuthenticatedRequest(url: string, options: any = {}) {
    const wpConnectionEnabled = process.env.WP_CONNECTION_ENABLED !== 'false';
    if (!wpConnectionEnabled) {
      throw new Error('WordPress connection is disabled via WP_CONNECTION_ENABLED=false');
    }

    await this.authenticate();

    if (!this.authCookie) {
      throw new Error('No authentication cookie available');
    }

    const headers = {
      Accept: 'application/json, text/plain, */*',
      Cookie: this.authCookie,
      'User-Agent':
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36',
      ...options.headers,
    };

    // Add nonce if available
    if (this.nonce) {
      headers['X-WP-Nonce'] = this.nonce;
    }

    try {
      return await this.axiosInstance({
        ...options,
        url,
        headers,
      });
    } catch (error: any) {
      // If we get a 401, clear the cookie and retry once
      if (error.response?.status === 401) {
        logger.warn('WordPress request failed with 401, clearing cookie and retrying...');
        this.authCookie = null;
        this.nonce = null;
        this.lastAuthTime = 0;
      }
      throw error;
    }
  }

  private async verifyAuthentication(): Promise<void> {
    try {
      const verifyUrl = 'https://entry4sports.co.uk/wp-json/e4s/v5/public/config?x=s7h1o2t2t3h';

      const headers = {
        Accept: 'application/json, text/plain, */*',
        Cookie: this.authCookie,
        'User-Agent':
          'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36',
      };

      // Add nonce if available
      if (this.nonce) {
        (headers as Record<string, string>)['X-WP-Nonce'] = this.nonce;
      }

      logger.debug('WordPress verification request headers:', {
        url: verifyUrl,
        cookieLength: this.authCookie?.length || 0,
        hasNonce: !!this.nonce,
      });

      const verifyResponse = await this.axiosInstance.get(verifyUrl, { headers });

      logger.info('WordPress authentication verification successful:', {
        status: verifyResponse.status,
        dataKeys: Object.keys(verifyResponse.data || {}),
        responseSize: JSON.stringify(verifyResponse.data || {}).length,
      });
    } catch (error: any) {
      logger.error('WordPress authentication verification failed:', {
        error: error instanceof Error ? error.message : String(error),
        status: error.response?.status,
        statusText: error.response?.statusText,
        responseData: error.response?.data,
        cookiePreview: this.authCookie?.substring(0, 100) + '...',
        url: 'https://entry4sports.co.uk/wp-json/e4s/v5/public/config?x=s7h1o2t2t3h',
      });
      throw new Error(`Authentication verification failed: ${error.message}`);
    }
  }
}

export const wpAuthService = new WordPressAuthService();
