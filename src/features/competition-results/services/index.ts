import { dbConnect } from '../../../shared/lib/db';
import { CompetitionResult } from '../models/types';

const competitionResultService = {
  getByCompetitionId: async (competitionId: number): Promise<CompetitionResult[]> => {
    const sql = `select * from Entry4_uk_ResultsView where compid = ?`;

    return await dbConnect.query<CompetitionResult[]>(sql, [competitionId]);
  },

  getByCategory: async (
    competitionId: number,
    categoryId: number
  ): Promise<CompetitionResult[]> => {
    const sql = `
      SELECT 
        r.id,
        r.competition_id as competitionId,
        r.category_id as categoryId,
        c.name as categoryName,
        r.athlete_id as athleteId,
        a.name as athleteName,
        r.team_id as teamId,
        t.team_name as teamName,
        r.rank,
        r.score,
        r.notes
      FROM 
        competition_results r
      JOIN 
        categories c ON r.category_id = c.id
      LEFT JOIN 
        athletes a ON r.athlete_id = a.id
      LEFT JOIN 
        competition_teams t ON r.team_id = t.id
      WHERE 
        r.competition_id = ? AND r.category_id = ?
      ORDER BY 
        r.rank
    `;

    return await dbConnect.query<CompetitionResult[]>(sql, [competitionId, categoryId]);
  },

  getByAthlete: async (athleteId: number): Promise<CompetitionResult[]> => {
    const sql = `
      SELECT 
        r.id,
        r.competition_id as competitionId,
        r.category_id as categoryId,
        c.name as categoryName,
        r.athlete_id as athleteId,
        a.name as athleteName,
        r.team_id as teamId,
        t.team_name as teamName,
        r.rank,
        r.score,
        r.notes
      FROM 
        competition_results r
      JOIN 
        categories c ON r.category_id = c.id
      JOIN 
        athletes a ON r.athlete_id = a.id
      LEFT JOIN 
        competition_teams t ON r.team_id = t.id
      WHERE 
        r.athlete_id = ?
      ORDER BY 
        r.competition_id DESC, c.name
    `;

    return await dbConnect.query<CompetitionResult[]>(sql, [athleteId]);
  },
};

export { competitionResultService };
