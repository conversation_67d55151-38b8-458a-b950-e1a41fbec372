import { RowDataPacket } from 'mysql2/promise';
import { StringifiedJSON } from '../../../shared/types/common';

/*
{
  "rhId": 16427,
  "compId": 617,
  "egId": 13737,
  "rdId": 196151,
  "heatNo": 1,
  "laneNo": 5,
  "position": 1,
  "athleteId": 107472,
  "athlete": "Noah HANSON",
  "club": "Essex",
  "bibNo": "27",
  "score": "13.64",
  "qualify": "Q",
  "scoreText": "",
  "ageGroup": "",
  "gender": "",
  "wind": "+1.0",
  "eaAward": 9,
  "resultKey": "h1",
  "resultValue": "13.64",
  "crOptions": "{\"scoreText\":\"\",\"place\":1,\"bibNo\":27,\"lane\":5,\"club\":\"Essex\",\"athlete\":\"<PERSON>ANSO<PERSON>\",\"firstName\":\"Noah\",\"surName\":\"HANSON\",\"athleteId\":107472,\"time\":13.64,\"ageGroup\":\"Inters\",\"ws\":1,\"eventTime\":\"10:00:00\"}"
}
*/
export interface CompetitionResult extends RowDataPacket {
  rhId: number;
  compId: number;
  egId: number;
  rdId: number;
  heatNo: number;
  laneNo: number;
  position: number;
  athleteId: number;
  athlete: string;
  club: string;
  bibNo: string;
  score: string;
  qualify: string;
  scoreText: string;
  ageGroup: string;
  gender: string;
  wind: string;
  eaAward: number;
  resultKey: string;
  resultValue: string;
  crOptions: StringifiedJSON<CrOptions>;
}

export interface CrOptions {
  scoreText: string;
  place: number;
  bibNo: number;
  lane: number;
  club: string;
  athlete: string;
  firstName: string;
  surName: string;
  athleteId: number;
  time: number;
  ageGroup: string;
  ws: number;
  eventTime: string;
}

// export interface CompetitionResult extends RowDataPacket {
//   id: number;
//   competitionId: number;
//   categoryId: number;
//   categoryName: string;
//   athleteId?: number;
//   athleteName?: string;
//   teamId?: number;
//   teamName?: string;
//   rank: number;
//   score: number;
//   notes?: string;
//   // Add other fields based on your SQL query results
// }
