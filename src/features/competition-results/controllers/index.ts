import { Request, Response, NextFunction } from 'express';
import { competitionResultService } from '../services';
import { logger } from '../../../shared/lib/logger';

export const getResultsByCompetition = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const competitionId = parseInt(req.params.competitionId, 10);
    
    if (isNaN(competitionId)) {
      res.status(400).json({ message: 'Invalid competition ID' });
      return;
    }
    
    const results = await competitionResultService.getByCompetitionId(competitionId);
    res.json({ data: results });
  } catch (error) {
    next(error);
  }
};

export const getResultsByCategory = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const competitionId = parseInt(req.params.competitionId, 10);
    const categoryId = parseInt(req.params.categoryId, 10);
    
    if (isNaN(competitionId) || isNaN(categoryId)) {
      res.status(400).json({ message: 'Invalid competition or category ID' });
      return;
    }
    
    const results = await competitionResultService.getByCategory(competitionId, categoryId);
    res.json({ data: results });
  } catch (error) {
    next(error);
  }
};

export const getResultsByAthlete = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const athleteId = parseInt(req.params.athleteId, 10);
    
    if (isNaN(athleteId)) {
      res.status(400).json({ message: 'Invalid athlete ID' });
      return;
    }
    
    const results = await competitionResultService.getByAthlete(athleteId);
    res.json({ data: results });
  } catch (error) {
    next(error);
  }
};