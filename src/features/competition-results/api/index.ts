import express from 'express';
import { getResultsByCompetition, getResultsByCategory, getResultsByAthlete } from '../controllers';

const resultRouter = express.Router();

// Add debugging middleware
resultRouter.use((req, res, next) => {
  console.log(`RESULTS ROUTER: ${req.method} ${req.originalUrl} (${req.url})`);
  next();
});

// Routes for results (read-only)
resultRouter.get('/competition/:competitionId', getResultsByCompetition);
resultRouter.get('/competition/:competitionId/category/:categoryId', getResultsByCategory);
resultRouter.get('/athlete/:athleteId', getResultsByAthlete);

// Add a test route to verify the router is working
resultRouter.get('/test', (req, res) => {
  res.json({ message: 'Results router test route is working!' });
});

// Export the router
export { resultRouter };
