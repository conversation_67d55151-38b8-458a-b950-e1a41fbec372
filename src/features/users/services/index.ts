import { RowDataPacket } from 'mysql2/promise';
import { dbConnect } from '../../../shared/lib/db';

interface User extends RowDataPacket {
  id: number;
  username: string;
  email: string;
  // Add other fields as needed
}

const userService = {
  getAll: async (): Promise<User[]> => {
    return await dbConnect.query<User[]>('SELECT * FROM users');
  },

  getById: async (id: number): Promise<User | undefined> => {
    const users = await dbConnect.query<User[]>('SELECT * FROM users WHERE id = ?', [id]);
    return users[0];
  },
};

export { userService, User };
