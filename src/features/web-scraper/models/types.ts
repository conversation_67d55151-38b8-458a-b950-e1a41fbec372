export interface ScheduleEvent {
  plannedTime: string;
  eventName: string;
  eventLink: string;
  phase: string;
  category: string;
  gender: string;
  participants: number;
  heats: number;
  status: string;
  actualTime: string;
}

export interface AthleteResult {
  rank: number;
  lane: number;
  athleteId: string;
  name: string;
  personalBest: string;
  team: string;
  category: string;
  result: string;
  info: string;
}

export interface HeatResult {
  eventName: string;
  heatNumber: number;
  eventLink: string;
  nextHeatLink?: string;
  athletes: AthleteResult[];
}

export interface CompetitionData {
  baseUrl: string;
  scheduleUrl: string;
  events: ScheduleEvent[];
  heats: HeatResult[];
  scrapedAt: Date;
}

export interface ScrapingProgress {
  sessionId: string;
  status: 'idle' | 'running' | 'completed' | 'error';
  startTime?: Date;
  endTime?: Date;
  totalEvents: number;
  processedEvents: number;
  totalHeats: number;
  processedHeats: number;
  currentEvent?: string;
  currentHeat?: number;
  estimatedTimeRemaining?: number;
  averageEventTime?: number;
  errors: ScrapingError[];
  warnings: string[];
  dataQuality: DataQualityMetrics;
}

export interface ScrapingError {
  timestamp: Date;
  type: 'network' | 'parsing' | 'validation' | 'unknown';
  message: string;
  context?: string;
  retryable: boolean;
}

export interface DataQualityMetrics {
  totalAthletes: number;
  athletesWithResults: number;
  athletesWithoutResults: number;
  eventsWithAllHeats: number;
  eventsWithMissingHeats: number;
  averageAthletesPerHeat: number;
}
