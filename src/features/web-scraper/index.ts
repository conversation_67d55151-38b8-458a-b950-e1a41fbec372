// Public API for the web-scraper feature
import { scraperRouter } from './api';
import { scrapeCompetition, getScrapingProgress } from './controllers';
import { WebScraperService, CompetitionScraper, DataExporter } from './services';
import { CompetitionData, ScheduleEvent, HeatResult, AthleteResult } from './models/types';

export {
  scraperRouter,
  scrapeCompetition,
  getScrapingProgress,
  WebScraperService,
  CompetitionScraper,
  DataExporter,
  CompetitionData,
  ScheduleEvent,
  HeatResult,
  AthleteResult,
};