import express from 'express';
import { scrapeCompetition, getScrapingProgress } from '../controllers';

const scraperRouter = express.Router();

// Ensure JSON parsing middleware is applied (redundant but safe)
scraperRouter.use(express.json());

// Add debugging middleware
scraperRouter.use((req, res, next) => {
  console.log(`SCRAPER ROUTER: ${req.method} ${req.originalUrl} (${req.url})`);
  console.log('Request headers:', req.headers);
  console.log('Content-Type:', req.get('Content-Type'));
  console.log('Request body:', req.body);
  console.log('Request body type:', typeof req.body);
  console.log('Request body keys:', req.body ? Object.keys(req.body) : 'No body');
  next();
});

// Routes for web scraping
scraperRouter.post('/scrape', scrapeCompetition);
scraperRouter.get('/progress', getScrapingProgress);

// Add a test route for GET
scraperRouter.get('/test', (req, res) => {
  res.json({ message: 'Web scraper router test route is working!' });
});

// Add a test route for POST to verify body parsing
scraperRouter.post('/test', (req, res) => {
  res.json({
    message: 'Web scraper POST test route is working!',
    receivedBody: req.body,
    bodyType: typeof req.body,
    contentType: req.get('Content-Type')
  });
});

export { scraperRouter };
