import { WebFetcher } from '../services/web-fetcher';
import axios from 'axios';
import { vi } from 'vitest';

// Mock axios
vi.mock('axios', () => ({
  default: {
    get: vi.fn(),
  },
}));

// Create typed mock
const mockAxiosGet = vi.fn();
(axios as any).get = mockAxiosGet;

describe('WebFetcher', () => {
  let webFetcher: WebFetcher;

  beforeEach(() => {
    webFetcher = new WebFetcher({
      timeout: 5000,
      retryAttempts: 2,
      retryDelay: 100,
      enableCache: false,
      enableRateLimit: false
    }); // Short timeout and retry for tests
    vi.clearAllMocks();
  });

  describe('URL validation', () => {
    it('should reject invalid URLs', async () => {
      await expect(webFetcher.fetchHtml('not-a-url')).rejects.toThrow('Invalid URL provided');
      await expect(webFetcher.fetchHtml('ftp://example.com')).rejects.toThrow('Invalid URL provided');
    });

    it('should accept valid HTTP/HTTPS URLs', async () => {
      mockAxiosGet.mockResolvedValueOnce({
        status: 200,
        data: '<html><body>Test content</body></html>',
      });

      const result = await webFetcher.fetchHtml('https://example.com');
      expect(result).toBe('<html><body>Test content</body></html>');
    });
  });

  describe('Content validation', () => {
    it('should reject empty content', async () => {
      mockAxiosGet.mockResolvedValueOnce({
        status: 200,
        data: '',
      });

      await expect(webFetcher.fetchHtml('https://example.com')).rejects.toThrow('empty or invalid HTML content');
    });

    it('should warn about short content', async () => {
      const consoleSpy = vi.spyOn(console, 'warn').mockImplementation(() => {});

      mockAxiosGet.mockResolvedValueOnce({
        status: 200,
        data: '<html>short</html>',
      });

      const result = await webFetcher.fetchHtml('https://example.com');
      expect(result).toBe('<html>short</html>');

      consoleSpy.mockRestore();
    });

    it('should detect error pages', async () => {
      mockAxiosGet.mockResolvedValueOnce({
        status: 200,
        data: '<html><title>404 Not Found</title><body>Page not found</body></html>',
      });

      await expect(webFetcher.fetchHtml('https://example.com')).rejects.toThrow('error page instead of valid content');
    });
  });

  describe('Retry logic', () => {
    it('should retry on network errors', async () => {
      mockAxiosGet
        .mockRejectedValueOnce(new Error('Network error'))
        .mockResolvedValueOnce({
          status: 200,
          data: '<html><body>Success after retry</body></html>',
        });

      const result = await webFetcher.fetchHtml('https://example.com');
      expect(result).toBe('<html><body>Success after retry</body></html>');
      expect(mockAxiosGet).toHaveBeenCalledTimes(2);
    });

    it('should fail after max retries', async () => {
      mockAxiosGet.mockRejectedValue(new Error('Persistent network error'));

      await expect(webFetcher.fetchHtml('https://example.com')).rejects.toThrow('Failed to fetch');
      expect(mockAxiosGet).toHaveBeenCalledTimes(2); // Initial + 1 retry
    });

    it('should handle different error types', async () => {
      const connectionError = new Error('Connection refused');
      (connectionError as any).code = 'ECONNREFUSED';

      mockAxiosGet.mockRejectedValueOnce(connectionError);

      await expect(webFetcher.fetchHtml('https://example.com')).rejects.toThrow('Connection refused');
    });
  });

  describe('HTTP status handling', () => {
    it('should handle non-200 status codes', async () => {
      mockAxiosGet.mockResolvedValueOnce({
        status: 404,
        statusText: 'Not Found',
        data: 'Not found',
      });

      await expect(webFetcher.fetchHtml('https://example.com')).rejects.toThrow('HTTP 404: Not Found');
    });
  });
});
