import { ProgressManager } from '../services/progress-manager';

describe('ProgressManager', () => {
  let progressManager: ProgressManager;

  beforeEach(() => {
    progressManager = new ProgressManager();
  });

  describe('Session management', () => {
    it('should create a new session', () => {
      const progress = progressManager.createSession('test-session');

      expect(progress.sessionId).toBe('test-session');
      expect(progress.status).toBe('idle');
      expect(progress.totalEvents).toBe(0);
      expect(progress.processedEvents).toBe(0);
      expect(progress.errors).toEqual([]);
    });

    it('should start a session', () => {
      progressManager.createSession('test-session');
      progressManager.startSession('test-session', 5);

      const progress = progressManager.getProgress('test-session');
      expect(progress?.status).toBe('running');
      expect(progress?.totalEvents).toBe(5);
      expect(progress?.startTime).toBeDefined();
    });

    it('should complete a session successfully', () => {
      progressManager.createSession('test-session');
      progressManager.startSession('test-session', 5);
      progressManager.completeSession('test-session', true);

      const progress = progressManager.getProgress('test-session');
      expect(progress?.status).toBe('completed');
      expect(progress?.endTime).toBeDefined();
    });

    it('should complete a session with error', () => {
      progressManager.createSession('test-session');
      progressManager.startSession('test-session', 5);
      progressManager.completeSession('test-session', false);

      const progress = progressManager.getProgress('test-session');
      expect(progress?.status).toBe('error');
    });
  });

  describe('Progress tracking', () => {
    beforeEach(() => {
      progressManager.createSession('test-session');
      progressManager.startSession('test-session', 3);
    });

    it('should update event progress', () => {
      const eventStartTime = Date.now() - 1000; // 1 second ago
      progressManager.updateEventProgress('test-session', 'Event 1', eventStartTime);

      const progress = progressManager.getProgress('test-session');
      expect(progress?.currentEvent).toBe('Event 1');
      expect(progress?.processedEvents).toBe(1);
      expect(progress?.averageEventTime).toBeGreaterThan(0);
      expect(progress?.estimatedTimeRemaining).toBeGreaterThan(0);
    });

    it('should update heat progress', () => {
      progressManager.updateHeatProgress('test-session', 2);

      const progress = progressManager.getProgress('test-session');
      expect(progress?.currentHeat).toBe(2);
      expect(progress?.processedHeats).toBe(1);
    });

    it('should calculate estimated time remaining', () => {
      // Simulate processing events with known timing
      const baseTime = Date.now();
      progressManager.updateEventProgress('test-session', 'Event 1', baseTime - 2000); // 2s
      progressManager.updateEventProgress('test-session', 'Event 2', baseTime - 1000); // 1s

      const progress = progressManager.getProgress('test-session');
      expect(progress?.averageEventTime).toBe(1500); // Average of 2000ms and 1000ms
      expect(progress?.estimatedTimeRemaining).toBe(1500); // 1 remaining event * 1500ms average
    });
  });

  describe('Error and warning handling', () => {
    beforeEach(() => {
      progressManager.createSession('test-session');
    });

    it('should add errors', () => {
      const error = progressManager.createError('network', 'Connection failed', 'https://example.com', true);
      progressManager.addError('test-session', error);

      const progress = progressManager.getProgress('test-session');
      expect(progress?.errors).toHaveLength(1);
      expect(progress?.errors[0].type).toBe('network');
      expect(progress?.errors[0].message).toBe('Connection failed');
      expect(progress?.errors[0].retryable).toBe(true);
    });

    it('should add warnings', () => {
      progressManager.addWarning('test-session', 'Data quality issue');

      const progress = progressManager.getProgress('test-session');
      expect(progress?.warnings).toHaveLength(1);
      expect(progress?.warnings[0]).toBe('Data quality issue');
    });

    it('should create error objects with correct structure', () => {
      const error = progressManager.createError('parsing', 'Invalid HTML', 'event.html', false);

      expect(error.type).toBe('parsing');
      expect(error.message).toBe('Invalid HTML');
      expect(error.context).toBe('event.html');
      expect(error.retryable).toBe(false);
      expect(error.timestamp).toBeInstanceOf(Date);
    });
  });

  describe('Data quality tracking', () => {
    beforeEach(() => {
      progressManager.createSession('test-session');
    });

    it('should update data quality metrics', () => {
      progressManager.updateDataQuality('test-session', {
        totalAthletes: 50,
        athletesWithResults: 45,
        athletesWithoutResults: 5,
      });

      const progress = progressManager.getProgress('test-session');
      expect(progress?.dataQuality.totalAthletes).toBe(50);
      expect(progress?.dataQuality.athletesWithResults).toBe(45);
      expect(progress?.dataQuality.athletesWithoutResults).toBe(5);
    });

    it('should calculate average athletes per heat on completion', () => {
      progressManager.updateDataQuality('test-session', { totalAthletes: 100 });
      progressManager.updateHeatProgress('test-session', 1);
      progressManager.updateHeatProgress('test-session', 2);
      progressManager.updateHeatProgress('test-session', 3);
      progressManager.updateHeatProgress('test-session', 4);
      progressManager.updateHeatProgress('test-session', 5); // 5 heats total

      progressManager.completeSession('test-session', true);

      const progress = progressManager.getProgress('test-session');
      expect(progress?.dataQuality.averageAthletesPerHeat).toBe(20); // 100 athletes / 5 heats
    });
  });

  describe('Multiple sessions', () => {
    it('should handle multiple concurrent sessions', () => {
      progressManager.createSession('session-1');
      progressManager.createSession('session-2');

      progressManager.startSession('session-1', 3);
      progressManager.startSession('session-2', 5);

      const allSessions = progressManager.getAllSessions();
      expect(allSessions).toHaveLength(2);

      const session1 = progressManager.getProgress('session-1');
      const session2 = progressManager.getProgress('session-2');

      expect(session1?.totalEvents).toBe(3);
      expect(session2?.totalEvents).toBe(5);
    });
  });

  describe('Event emission', () => {
    it('should emit progress events', (done: () => void) => {
      progressManager.createSession('test-session');

      progressManager.on('progress', (data) => {
        expect(data.sessionId).toBe('test-session');
        expect(data.progress).toBeDefined();
        done();
      });

      progressManager.startSession('test-session', 1);
    });
  });
});
