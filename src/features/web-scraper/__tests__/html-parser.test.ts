import { HtmlParser } from '../services/html-parser';

describe('HtmlParser', () => {
  let parser: HtmlParser;

  beforeEach(() => {
    parser = new HtmlParser();
  });

  // Mock HTML data used across multiple test suites
  const mockHeatHtml = `
    <html>
      <body>
        <div class="z3-hide">
          <a class="next" href="event001h02.html">Next Heat</a>
        </div>
        <table>
          <tr class="odd">
            <td class="rank">1</td>
            <td class="lane">4</td>
            <td class="id">12345</td>
            <td class="name"><PERSON></td>
            <td class="pb">50.23</td>
            <td class="team"><span>Team A</span></td>
            <td class="cat">Senior</td>
            <td class="result">49.87</td>
            <td class="info">PB</td>
          </tr>
          <tr class="even">
            <td class="rank">2</td>
            <td class="lane">5</td>
            <td class="id">67890</td>
            <td class="name">Jane Doe</td>
            <td class="pb">51.45</td>
            <td class="team"><span>Team B</span></td>
            <td class="cat">Senior</td>
            <td class="result">50.12</td>
            <td class="info"></td>
          </tr>
        </table>
      </body>
    </html>
  `;

  describe('parseSchedule', () => {
    const mockScheduleHtml = `
      <html>
        <body>
          <table>
            <tr class="odd">
              <td class="plannedtime">09:00</td>
              <td class="eventname"><a href="event001h01.html">100m Freestyle Men</a></td>
              <td class="phase">Final</td>
              <td class="cat">Senior</td>
              <td class="gender">M</td>
              <td class="participants">8</td>
              <td class="heats">1</td>
              <td class="status">Completed</td>
              <td class="time">09:15</td>
            </tr>
            <tr class="even">
              <td class="plannedtime">09:30</td>
              <td class="eventname"><a href="event002h01.html">200m Backstroke Women</a></td>
              <td class="phase">Heat</td>
              <td class="cat">Senior</td>
              <td class="gender">F</td>
              <td class="participants">16</td>
              <td class="heats">2</td>
              <td class="status">In Progress</td>
              <td class="time"></td>
            </tr>
          </table>
        </body>
      </html>
    `;

    it('should parse schedule events correctly', () => {
      const events = parser.parseSchedule(mockScheduleHtml, 'https://example.com/');

      expect(events).toHaveLength(2);

      expect(events[0]).toEqual({
        plannedTime: '09:00',
        eventName: '100m Freestyle Men',
        eventLink: 'https://example.com/event001h01.html',
        phase: 'Final',
        category: 'Senior',
        gender: 'M',
        participants: 8,
        heats: 1,
        status: 'Completed',
        actualTime: '09:15',
      });

      expect(events[1]).toEqual({
        plannedTime: '09:30',
        eventName: '200m Backstroke Women',
        eventLink: 'https://example.com/event002h01.html',
        phase: 'Heat',
        category: 'Senior',
        gender: 'F',
        participants: 16,
        heats: 2,
        status: 'In Progress',
        actualTime: '',
      });
    });

    it('should handle invalid inputs', () => {
      expect(() => parser.parseSchedule('', 'https://example.com/')).toThrow('Invalid HTML content');
      expect(() => parser.parseSchedule(mockScheduleHtml, '')).toThrow('Invalid base URL');
    });

    it('should handle empty schedule', () => {
      const emptyHtml = '<html><body><table></table></body></html>';
      expect(() => parser.parseSchedule(emptyHtml, 'https://example.com/')).toThrow('No valid events found');
    });
  });

  describe('parseHeatResults', () => {

    it('should parse heat results correctly', () => {
      const result = parser.parseHeatResults(
        mockHeatHtml,
        '100m Freestyle Men',
        'https://example.com/event001h01.html'
      );

      expect(result.eventName).toBe('100m Freestyle Men');
      expect(result.heatNumber).toBe(1);
      expect(result.eventLink).toBe('https://example.com/event001h01.html');
      expect(result.nextHeatLink).toBe('https://example.com/event001h02.html');
      expect(result.athletes).toHaveLength(2);

      expect(result.athletes[0]).toEqual({
        rank: 1,
        lane: 4,
        athleteId: '12345',
        name: 'John Smith',
        personalBest: '50.23',
        team: 'Team A',
        category: 'Senior',
        result: '49.87',
        info: 'PB',
      });
    });

    it('should handle missing next heat link', () => {
      const htmlWithoutNext = mockHeatHtml.replace(/<a class="next"[^>]*>.*?<\/a>/, '');

      const result = parser.parseHeatResults(
        htmlWithoutNext,
        '100m Freestyle Men',
        'https://example.com/event001h01.html'
      );

      expect(result.nextHeatLink).toBeUndefined();
    });

    it('should extract heat number from URL', () => {
      const result = parser.parseHeatResults(
        mockHeatHtml,
        'Test Event',
        'https://example.com/event001h05.html'
      );

      expect(result.heatNumber).toBe(5);
    });
  });

  describe('URL resolution', () => {
    it('should resolve next heat URLs correctly', () => {
      const result = parser.parseHeatResults(
        mockHeatHtml,
        'Test Event',
        'https://example.com/path/event001h01.html'
      );

      expect(result.nextHeatLink).toBe('https://example.com/path/event001h02.html');
    });

    it('should handle absolute URLs in next heat link', () => {
      const htmlWithAbsoluteUrl = mockHeatHtml.replace(
        'event001h02.html',
        'https://other.com/event001h02.html'
      );

      const result = parser.parseHeatResults(
        htmlWithAbsoluteUrl,
        'Test Event',
        'https://example.com/event001h01.html'
      );

      expect(result.nextHeatLink).toBe('https://other.com/event001h02.html');
    });
  });
});
