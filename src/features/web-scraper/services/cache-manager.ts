import { logger } from '../../../shared/lib/logger';

export interface CacheEntry<T> {
  data: T;
  timestamp: Date;
  expiresAt: Date;
  hits: number;
}

export interface CacheOptions {
  ttl: number; // Time to live in milliseconds
  maxSize: number; // Maximum number of entries
  cleanupInterval: number; // Cleanup interval in milliseconds
}

export class CacheManager<T> {
  private cache: Map<string, CacheEntry<T>> = new Map();
  private options: CacheOptions;
  private cleanupTimer?: NodeJS.Timeout;

  constructor(options: Partial<CacheOptions> = {}) {
    this.options = {
      ttl: options.ttl || 300000, // 5 minutes default
      maxSize: options.maxSize || 1000,
      cleanupInterval: options.cleanupInterval || 60000, // 1 minute
    };

    this.startCleanupTimer();
  }

  set(key: string, data: T, customTtl?: number): void {
    const ttl = customTtl || this.options.ttl;
    const now = new Date();
    const expiresAt = new Date(now.getTime() + ttl);

    // Remove oldest entries if cache is full
    if (this.cache.size >= this.options.maxSize) {
      this.evictOldest();
    }

    const entry: CacheEntry<T> = {
      data,
      timestamp: now,
      expiresAt,
      hits: 0,
    };

    this.cache.set(key, entry);
    logger.debug(`Cache: Set key ${key} (expires at ${expiresAt.toISOString()})`);
  }

  get(key: string): T | undefined {
    const entry = this.cache.get(key);
    
    if (!entry) {
      logger.debug(`Cache: Miss for key ${key}`);
      return undefined;
    }

    if (this.isExpired(entry)) {
      this.cache.delete(key);
      logger.debug(`Cache: Expired key ${key} removed`);
      return undefined;
    }

    entry.hits++;
    logger.debug(`Cache: Hit for key ${key} (${entry.hits} hits)`);
    return entry.data;
  }

  has(key: string): boolean {
    const entry = this.cache.get(key);
    if (!entry) return false;
    
    if (this.isExpired(entry)) {
      this.cache.delete(key);
      return false;
    }
    
    return true;
  }

  delete(key: string): boolean {
    const deleted = this.cache.delete(key);
    if (deleted) {
      logger.debug(`Cache: Deleted key ${key}`);
    }
    return deleted;
  }

  clear(): void {
    const size = this.cache.size;
    this.cache.clear();
    logger.info(`Cache: Cleared ${size} entries`);
  }

  getStats() {
    const entries = Array.from(this.cache.values());
    const expired = entries.filter(entry => this.isExpired(entry)).length;
    const totalHits = entries.reduce((sum, entry) => sum + entry.hits, 0);
    
    return {
      size: this.cache.size,
      expired,
      totalHits,
      averageHits: entries.length > 0 ? totalHits / entries.length : 0,
      oldestEntry: entries.length > 0 ? 
        Math.min(...entries.map(e => e.timestamp.getTime())) : null,
      newestEntry: entries.length > 0 ? 
        Math.max(...entries.map(e => e.timestamp.getTime())) : null,
    };
  }

  private isExpired(entry: CacheEntry<T>): boolean {
    return new Date() > entry.expiresAt;
  }

  private evictOldest(): void {
    let oldestKey: string | undefined;
    let oldestTime = Date.now();

    for (const [key, entry] of this.cache.entries()) {
      if (entry.timestamp.getTime() < oldestTime) {
        oldestTime = entry.timestamp.getTime();
        oldestKey = key;
      }
    }

    if (oldestKey) {
      this.cache.delete(oldestKey);
      logger.debug(`Cache: Evicted oldest key ${oldestKey}`);
    }
  }

  private cleanup(): void {
    const beforeSize = this.cache.size;
    const expiredKeys: string[] = [];

    for (const [key, entry] of this.cache.entries()) {
      if (this.isExpired(entry)) {
        expiredKeys.push(key);
      }
    }

    expiredKeys.forEach(key => this.cache.delete(key));
    
    if (expiredKeys.length > 0) {
      logger.debug(`Cache: Cleaned up ${expiredKeys.length} expired entries (${beforeSize} -> ${this.cache.size})`);
    }
  }

  private startCleanupTimer(): void {
    this.cleanupTimer = setInterval(() => {
      this.cleanup();
    }, this.options.cleanupInterval);
  }

  destroy(): void {
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer);
      this.cleanupTimer = undefined;
    }
    this.clear();
    logger.info('Cache: Destroyed');
  }

  // Helper method to create cache keys
  static createKey(...parts: (string | number)[]): string {
    return parts.map(part => String(part)).join(':');
  }
}
