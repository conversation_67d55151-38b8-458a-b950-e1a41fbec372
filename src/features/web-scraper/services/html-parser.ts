import * as cheerio from 'cheerio';
import { logger } from '../../../shared/lib/logger';
import { ScheduleEvent, AthleteResult, HeatResult } from '../models/types';

export class HtmlParser {
  parseSchedule(html: string, baseUrl: string): ScheduleEvent[] {
    logger.info('Parsing schedule HTML');

    // Validate inputs
    if (!html || typeof html !== 'string') {
      throw new Error('Invalid HTML content provided for schedule parsing');
    }

    if (!baseUrl || typeof baseUrl !== 'string') {
      throw new Error('Invalid base URL provided for schedule parsing');
    }

    const $ = cheerio.load(html);
    const events: ScheduleEvent[] = [];

    // Check if we have the expected table structure
    const rows = $('tr.odd, tr.even');
    if (rows.length === 0) {
      logger.warn('No schedule rows found with expected classes (tr.odd, tr.even)');
      // Try alternative selectors
      const altRows = $('table tr').filter((i, el) => i > 0); // Skip header row
      if (altRows.length === 0) {
        throw new Error('No schedule data found in HTML - page structure may have changed');
      }
      logger.info(`Found ${altRows.length} rows using alternative selector`);
    }

    $('tr.odd, tr.even').each((index, element) => {
      try {
        const $row = $(element);
        const eventLink = $row.find('td.eventname a').attr('href');

        if (!eventLink) {
          logger.debug(`Skipping row ${index}: no event link found`);
          return;
        }

        const eventName = $row.find('td.eventname a').text().trim();
        if (!eventName) {
          logger.warn(`Row ${index}: event link found but no event name`);
          return;
        }

        const event: ScheduleEvent = {
          plannedTime: $row.find('td.plannedtime').text().trim(),
          eventName,
          eventLink: this.resolveUrl(baseUrl, eventLink),
          phase: $row.find('td.phase').text().trim(),
          category: $row.find('td.cat').text().trim(),
          gender: $row.find('td.gender').text().trim(),
          participants: parseInt($row.find('td.participants').text().trim()) || 0,
          heats: parseInt($row.find('td.heats').text().trim()) || 0,
          status: $row.find('td.status').text().trim(),
          actualTime: $row.find('td.time').text().trim(),
        };

        // Validate the parsed event
        if (this.isValidEvent(event)) {
          events.push(event);
          logger.debug(`Parsed event: ${event.eventName}`);
        } else {
          logger.warn(`Skipping invalid event at row ${index}:`, event);
        }
      } catch (error) {
        logger.error(`Error parsing schedule row ${index}:`, error);
      }
    });

    if (events.length === 0) {
      throw new Error('No valid events found in schedule - data may be malformed');
    }

    logger.info(`Parsed ${events.length} valid events from schedule`);
    return events;
  }

  parseHeatResults(html: string, eventName: string, eventLink: string): HeatResult {
    logger.info(`Parsing heat results for ${eventName}`);

    // Validate inputs
    if (!html || typeof html !== 'string') {
      throw new Error(`Invalid HTML content provided for heat parsing: ${eventName}`);
    }

    if (!eventName || !eventLink) {
      throw new Error('Event name and link are required for heat parsing');
    }

    const $ = cheerio.load(html);
    const athletes: AthleteResult[] = [];

    // Extract heat number from URL or page content
    const heatNumber = this.extractHeatNumber(eventLink);

    // Find next heat link with multiple selectors
    let nextHeatLink = $('.z3-hide a.next').attr('href');
    if (!nextHeatLink) {
      // Try alternative selectors
      nextHeatLink = $('a.next').attr('href') ||
                    $('a[href*="h0"]').attr('href') ||
                    $('a').filter((i, el) => $(el).text().toLowerCase().includes('next')).attr('href');
    }

    // Check if we have the expected table structure
    const resultRows = $('tr.odd, tr.even').filter((i, el) => $(el).find('td.rank').length > 0);
    if (resultRows.length === 0) {
      logger.warn(`No result rows found for ${eventName} heat ${heatNumber}`);
      // This might be a heat with no results yet, which is valid
    }

    $('tr.odd, tr.even').each((index, element) => {
      try {
        const $row = $(element);

        // Skip if this doesn't look like a result row
        if (!$row.find('td.rank').length) return;

        const name = $row.find('td.name').text().trim();
        if (!name) {
          logger.debug(`Skipping row ${index}: no athlete name found`);
          return;
        }

        const athlete: AthleteResult = {
          rank: parseInt($row.find('td.rank').text().trim()) || 0,
          lane: parseInt($row.find('td.lane').text().trim()) || 0,
          athleteId: $row.find('td.id').text().trim(),
          name,
          personalBest: $row.find('td.pb').text().trim(),
          team: $row.find('td.team span').first().text().trim(),
          category: $row.find('td.cat').text().trim(),
          result: $row.find('td.result').text().trim(),
          info: $row.find('td.info').text().trim(),
        };

        // Validate athlete data
        if (this.isValidAthlete(athlete)) {
          athletes.push(athlete);
          logger.debug(`Parsed athlete: ${athlete.name} - ${athlete.result}`);
        } else {
          logger.warn(`Skipping invalid athlete at row ${index}:`, athlete);
        }
      } catch (error) {
        logger.error(`Error parsing athlete row ${index}:`, error);
      }
    });

    const heatResult: HeatResult = {
      eventName,
      heatNumber,
      eventLink,
      nextHeatLink: nextHeatLink ? this.resolveNextHeatUrl(eventLink, nextHeatLink) : undefined,
      athletes,
    };

    logger.info(`Parsed ${athletes.length} athletes for ${eventName} heat ${heatNumber}`);
    return heatResult;
  }

  private extractHeatNumber(url: string): number {
    const match = url.match(/h(\d+)\.html$/);
    return match ? parseInt(match[1]) : 1;
  }

  private resolveUrl(baseUrl: string, relativePath: string): string {
    if (relativePath.startsWith('http')) return relativePath;

    const base = baseUrl.endsWith('/') ? baseUrl.slice(0, -1) : baseUrl;
    const path = relativePath.startsWith('/') ? relativePath : `/${relativePath}`;

    return `${base}${path}`;
  }

  private resolveNextHeatUrl(currentHeatUrl: string, nextHeatRelativePath: string): string {
    if (nextHeatRelativePath.startsWith('http')) return nextHeatRelativePath;

    // Extract the base directory from the current heat URL
    // e.g., from "https://justiming.co.uk/liveresults/2025/bandbh14.7.25/event001h01.html"
    // get "https://justiming.co.uk/liveresults/2025/bandbh14.7.25/"
    const lastSlashIndex = currentHeatUrl.lastIndexOf('/');
    const baseDirectory = currentHeatUrl.substring(0, lastSlashIndex + 1);

    // Remove leading slash from relative path if present
    const cleanRelativePath = nextHeatRelativePath.startsWith('/')
      ? nextHeatRelativePath.substring(1)
      : nextHeatRelativePath;

    const resolvedUrl = `${baseDirectory}${cleanRelativePath}`;
    logger.debug(`Resolved next heat URL: ${currentHeatUrl} + ${nextHeatRelativePath} = ${resolvedUrl}`);

    return resolvedUrl;
  }

  private isValidEvent(event: ScheduleEvent): boolean {
    return !!(
      event.eventName &&
      event.eventLink &&
      event.eventName.length > 0 &&
      event.eventLink.startsWith('http')
    );
  }

  private isValidAthlete(athlete: AthleteResult): boolean {
    return !!(
      athlete.name &&
      athlete.name.length > 0 &&
      athlete.name !== 'N/A' &&
      athlete.name !== '-'
    );
  }
}
