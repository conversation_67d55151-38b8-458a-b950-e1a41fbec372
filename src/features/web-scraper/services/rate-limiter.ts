import { logger } from '../../../shared/lib/logger';

export interface RateLimitOptions {
  requestsPerSecond: number;
  burstSize: number;
  minDelay: number; // Minimum delay between requests in ms
  maxDelay: number; // Maximum delay for backoff in ms
  backoffMultiplier: number;
}

export interface RateLimitState {
  tokens: number;
  lastRefill: number;
  consecutiveErrors: number;
  currentDelay: number;
}

export class RateLimiter {
  private options: RateLimitOptions;
  private state: RateLimitState;
  private requestQueue: Array<{
    resolve: () => void;
    reject: (error: Error) => void;
    timestamp: number;
  }> = [];
  private processing = false;

  constructor(options: Partial<RateLimitOptions> = {}) {
    this.options = {
      requestsPerSecond: options.requestsPerSecond || 2, // Conservative default
      burstSize: options.burstSize || 5,
      minDelay: options.minDelay || 500,
      maxDelay: options.maxDelay || 10000,
      backoffMultiplier: options.backoffMultiplier || 2,
    };

    this.state = {
      tokens: this.options.burstSize,
      lastRefill: Date.now(),
      consecutiveErrors: 0,
      currentDelay: this.options.minDelay,
    };

    logger.info(`RateLimiter initialized: ${this.options.requestsPerSecond} req/s, burst: ${this.options.burstSize}`);
  }

  async waitForPermission(): Promise<void> {
    return new Promise((resolve, reject) => {
      this.requestQueue.push({
        resolve,
        reject,
        timestamp: Date.now(),
      });

      if (!this.processing) {
        this.processQueue();
      }
    });
  }

  onRequestSuccess(): void {
    // Reset error count and delay on successful request
    if (this.state.consecutiveErrors > 0) {
      logger.debug(`RateLimiter: Request succeeded, resetting error count (was ${this.state.consecutiveErrors})`);
      this.state.consecutiveErrors = 0;
      this.state.currentDelay = this.options.minDelay;
    }
  }

  onRequestError(error: any): void {
    this.state.consecutiveErrors++;
    
    // Increase delay for consecutive errors (exponential backoff)
    this.state.currentDelay = Math.min(
      this.state.currentDelay * this.options.backoffMultiplier,
      this.options.maxDelay
    );

    logger.warn(`RateLimiter: Request error (${this.state.consecutiveErrors} consecutive), delay increased to ${this.state.currentDelay}ms`);

    // If it's a rate limiting error, be more aggressive with backoff
    if (this.isRateLimitError(error)) {
      this.state.currentDelay = Math.min(this.state.currentDelay * 2, this.options.maxDelay);
      logger.warn(`RateLimiter: Rate limit detected, aggressive backoff to ${this.state.currentDelay}ms`);
    }
  }

  getStats() {
    return {
      tokens: this.state.tokens,
      queueLength: this.requestQueue.length,
      consecutiveErrors: this.state.consecutiveErrors,
      currentDelay: this.state.currentDelay,
      requestsPerSecond: this.options.requestsPerSecond,
      burstSize: this.options.burstSize,
    };
  }

  private async processQueue(): Promise<void> {
    this.processing = true;

    while (this.requestQueue.length > 0) {
      const request = this.requestQueue.shift();
      if (!request) break;

      try {
        await this.waitForToken();
        this.consumeToken();
        
        // Apply current delay
        if (this.state.currentDelay > 0) {
          await this.delay(this.state.currentDelay);
        }

        request.resolve();
      } catch (error) {
        request.reject(error instanceof Error ? error : new Error(String(error)));
      }
    }

    this.processing = false;
  }

  private async waitForToken(): Promise<void> {
    while (this.state.tokens <= 0) {
      this.refillTokens();
      if (this.state.tokens <= 0) {
        // Wait for next refill opportunity
        const waitTime = 1000 / this.options.requestsPerSecond;
        await this.delay(waitTime);
      }
    }
  }

  private refillTokens(): void {
    const now = Date.now();
    const timePassed = now - this.state.lastRefill;
    const tokensToAdd = Math.floor((timePassed / 1000) * this.options.requestsPerSecond);

    if (tokensToAdd > 0) {
      this.state.tokens = Math.min(
        this.state.tokens + tokensToAdd,
        this.options.burstSize
      );
      this.state.lastRefill = now;
      
      logger.debug(`RateLimiter: Refilled ${tokensToAdd} tokens (now ${this.state.tokens}/${this.options.burstSize})`);
    }
  }

  private consumeToken(): void {
    if (this.state.tokens > 0) {
      this.state.tokens--;
      logger.debug(`RateLimiter: Consumed token (${this.state.tokens}/${this.options.burstSize} remaining)`);
    }
  }

  private isRateLimitError(error: any): boolean {
    if (!error) return false;
    
    // Check for common rate limiting indicators
    const status = error.response?.status;
    const message = error.message?.toLowerCase() || '';
    
    return (
      status === 429 || // Too Many Requests
      status === 503 || // Service Unavailable
      message.includes('rate limit') ||
      message.includes('too many requests') ||
      message.includes('throttle')
    );
  }

  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  // Clean up old requests from queue (prevent memory leaks)
  private cleanupQueue(): void {
    const now = Date.now();
    const maxAge = 60000; // 1 minute
    
    const originalLength = this.requestQueue.length;
    this.requestQueue = this.requestQueue.filter(request => {
      const age = now - request.timestamp;
      if (age > maxAge) {
        request.reject(new Error('Request timed out in rate limiter queue'));
        return false;
      }
      return true;
    });

    if (this.requestQueue.length < originalLength) {
      logger.debug(`RateLimiter: Cleaned up ${originalLength - this.requestQueue.length} old requests`);
    }
  }

  destroy(): void {
    // Reject all pending requests
    while (this.requestQueue.length > 0) {
      const request = this.requestQueue.shift();
      if (request) {
        request.reject(new Error('Rate limiter destroyed'));
      }
    }
    
    this.processing = false;
    logger.info('RateLimiter: Destroyed');
  }
}
