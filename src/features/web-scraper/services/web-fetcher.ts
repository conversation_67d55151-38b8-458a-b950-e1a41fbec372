import axios, { AxiosResponse } from 'axios';
import { logger } from '../../../shared/lib/logger';
import { CacheManager } from './cache-manager';
import { RateLimiter } from './rate-limiter';

export interface WebFetcherOptions {
  timeout?: number;
  retryAttempts?: number;
  retryDelay?: number;
  enableCache?: boolean;
  cacheTtl?: number;
  enableRateLimit?: boolean;
  requestsPerSecond?: number;
}

export class WebFetcher {
  private readonly timeout: number;
  private readonly retryAttempts: number;
  private readonly retryDelay: number;
  private readonly cache?: CacheManager<string>;
  private readonly rateLimiter?: RateLimiter;

  constructor(options: WebFetcherOptions = {}) {
    this.timeout = options.timeout || 10000;
    this.retryAttempts = options.retryAttempts || 3;
    this.retryDelay = options.retryDelay || 1000;

    if (options.enableCache !== false) {
      this.cache = new CacheManager<string>({
        ttl: options.cacheTtl || 300000, // 5 minutes
        maxSize: 500,
      });
      logger.info('WebFetcher: Cache enabled');
    }

    if (options.enableRateLimit !== false) {
      this.rateLimiter = new RateLimiter({
        requestsPerSecond: options.requestsPerSecond || 2,
        burstSize: 5,
        minDelay: 500,
      });
      logger.info('WebFetcher: Rate limiting enabled');
    }
  }

  async fetchHtml(url: string): Promise<string> {
    // Validate URL
    if (!this.isValidUrl(url)) {
      throw new Error(`Invalid URL provided: ${url}`);
    }

    // Check cache first
    if (this.cache) {
      const cacheKey = CacheManager.createKey('html', url);
      const cached = this.cache.get(cacheKey);
      if (cached) {
        logger.debug(`Cache hit for: ${url}`);
        return cached;
      }
    }

    logger.info(`Fetching HTML from: ${url}`);

    // Wait for rate limiter permission
    if (this.rateLimiter) {
      await this.rateLimiter.waitForPermission();
    }

    for (let attempt = 1; attempt <= this.retryAttempts; attempt++) {
      try {
        const response: AxiosResponse<string> = await axios.get(url, {
          timeout: this.timeout,
          headers: {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
          },
        });

        if (response.status === 200) {
          const html = response.data;

          // Validate response content
          if (!html || typeof html !== 'string') {
            throw new Error('Received empty or invalid HTML content');
          }

          if (html.length < 100) {
            logger.warn(`Received suspiciously short HTML content (${html.length} chars) from ${url}`);
          }

          // Check for common error pages
          if (this.isErrorPage(html)) {
            throw new Error('Received error page instead of valid content');
          }

          logger.info(`Successfully fetched ${url} (${html.length} chars)`);

          // Cache the result
          if (this.cache) {
            const cacheKey = CacheManager.createKey('html', url);
            this.cache.set(cacheKey, html);
          }

          // Notify rate limiter of success
          if (this.rateLimiter) {
            this.rateLimiter.onRequestSuccess();
          }

          return html;
        }

        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      } catch (error) {
        const errorMessage = this.getErrorMessage(error);
        logger.warn(`Attempt ${attempt}/${this.retryAttempts} failed for ${url}: ${errorMessage}`);

        // Notify rate limiter of error
        if (this.rateLimiter) {
          this.rateLimiter.onRequestError(error);
        }

        if (attempt === this.retryAttempts) {
          logger.error(`Failed to fetch ${url} after ${this.retryAttempts} attempts`);
          throw new Error(`Failed to fetch ${url}: ${errorMessage}`);
        }

        // Wait before retrying (exponential backoff)
        await this.delay(this.retryDelay * Math.pow(2, attempt - 1));
      }
    }

    throw new Error(`Failed to fetch ${url} after all retry attempts`);
  }

  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  private isValidUrl(url: string): boolean {
    try {
      const urlObj = new URL(url);
      return urlObj.protocol === 'http:' || urlObj.protocol === 'https:';
    } catch {
      return false;
    }
  }

  private isErrorPage(html: string): boolean {
    const errorIndicators = [
      '404 not found',
      '500 internal server error',
      'page not found',
      'access denied',
      'forbidden',
      'error occurred',
      '<title>error',
      'something went wrong'
    ];

    const lowerHtml = html.toLowerCase();
    return errorIndicators.some(indicator => lowerHtml.includes(indicator));
  }

  private getErrorMessage(error: any): string {
    if (error.code === 'ECONNREFUSED') {
      return 'Connection refused - server may be down';
    }
    if (error.code === 'ENOTFOUND') {
      return 'DNS lookup failed - domain not found';
    }
    if (error.code === 'ETIMEDOUT') {
      return 'Request timed out';
    }
    if (error.response) {
      return `HTTP ${error.response.status}: ${error.response.statusText}`;
    }
    return error.message || 'Unknown error';
  }

  getStats() {
    return {
      cache: this.cache?.getStats(),
      rateLimiter: this.rateLimiter?.getStats(),
      options: {
        timeout: this.timeout,
        retryAttempts: this.retryAttempts,
        retryDelay: this.retryDelay,
      },
    };
  }

  destroy(): void {
    if (this.cache) {
      this.cache.destroy();
    }
    if (this.rateLimiter) {
      this.rateLimiter.destroy();
    }
    logger.info('WebFetcher: Destroyed');
  }
}
