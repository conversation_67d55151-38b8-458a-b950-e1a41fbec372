import axios from 'axios';
import { logger } from '../../../shared/lib/logger';
import { CompetitionData } from '../models/types';
import { wpAuthService } from '../../wordpress/services/wp-auth';

export interface ExportConfig {
  endpointUrl: string;
  apiKey?: string;
  timeout?: number;
  useWordPress?: boolean;
  competitionId?: number;
}

export class DataExporter {
  private config: ExportConfig;

  constructor(config: ExportConfig) {
    this.config = {
      timeout: 30000,
      ...config,
    };
  }

  async exportCompetitionData(data: CompetitionData): Promise<void> {
    logger.info(`Exporting competition data to: ${this.config.endpointUrl}`);

    try {
      if (this.config.useWordPress) {
        await this.exportToWordPress(data);
      } else {
        await this.exportToGenericEndpoint(data);
      }
    } catch (error) {
      logger.error('Failed to export competition data:', error);
      throw error;
    }
  }

  private async exportToWordPress(data: CompetitionData): Promise<void> {
    if (!this.config.competitionId) {
      throw new Error('Competition ID is required for WordPress export');
    }

    const wpDomain =
      process.env.WP_API_BASE_URL?.replace('/wp-json/wp/v2', '') || 'https://entry4sports.co.uk';
    const wpEndpoint = `${wpDomain}/wp-json/e4s/v5/competition/extresults/${this.config.competitionId}`;

    const payload = {
      competition: {
        baseUrl: data.baseUrl,
        scheduleUrl: data.scheduleUrl,
        scrapedAt: data.scrapedAt,
      },
      events: data.events,
      heats: data.heats,
      summary: {
        totalEvents: data.events.length,
        totalHeats: data.heats.length,
        totalAthletes: data.heats.reduce((sum, heat) => sum + heat.athletes.length, 0),
      },
    };

    logger.info(`Submitting scraped data to WordPress endpoint: ${wpEndpoint}`);

    const response = await wpAuthService.makeAuthenticatedRequest(wpEndpoint, {
      method: 'POST',
      data: payload,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (response.status >= 200 && response.status < 300) {
      logger.info(
        `Successfully exported competition data to WordPress. Response: ${response.status}`
      );
    } else {
      throw new Error(`WordPress export failed with status: ${response.status}`);
    }
  }

  private async exportToGenericEndpoint(data: CompetitionData): Promise<void> {
    const payload = {
      competition: {
        baseUrl: data.baseUrl,
        scheduleUrl: data.scheduleUrl,
        scrapedAt: data.scrapedAt,
      },
      events: data.events,
      heats: data.heats,
      summary: {
        totalEvents: data.events.length,
        totalHeats: data.heats.length,
        totalAthletes: data.heats.reduce((sum, heat) => sum + heat.athletes.length, 0),
      },
    };

    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
    };

    if (this.config.apiKey) {
      headers['Authorization'] = `Bearer ${this.config.apiKey}`;
    }

    const response = await axios.post(this.config.endpointUrl, payload, {
      headers,
      timeout: this.config.timeout,
    });

    if (response.status >= 200 && response.status < 300) {
      logger.info(`Successfully exported competition data. Response: ${response.status}`);
    } else {
      throw new Error(`Export failed with status: ${response.status}`);
    }
  }
}
