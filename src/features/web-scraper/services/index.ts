import { logger } from '../../../shared/lib/logger';
import { CompetitionScraper } from './scraper';
import { DataExporter, ExportConfig } from './data-exporter';
import { ProgressManager } from './progress-manager';
import { CompetitionData, ScrapingProgress } from '../models/types';

export class WebScraperService {
  private scraper: CompetitionScraper;
  private exporter?: DataExporter;
  private progressManager: ProgressManager;

  constructor(exportConfig?: ExportConfig) {
    this.progressManager = new ProgressManager();
    this.scraper = new CompetitionScraper(this.progressManager);
    if (exportConfig) {
      this.exporter = new DataExporter(exportConfig);
    }
  }

  async scrapeAndExport(scheduleUrl: string): Promise<CompetitionData> {
    logger.info(`Starting scrape and export process for: ${scheduleUrl}`);
    
    try {
      // Scrape the competition data
      const competitionData = await this.scraper.scrapeCompetition(scheduleUrl);
      
      // Export if exporter is configured
      if (this.exporter) {
        await this.exporter.exportCompetitionData(competitionData);
        logger.info('Scrape and export completed successfully');
      } else {
        logger.info('Scraping completed (no export configured)');
      }

      return competitionData;
    } catch (error) {
      logger.error('Scrape and export process failed:', error);
      throw error;
    }
  }

  async scrapeOnly(scheduleUrl: string): Promise<CompetitionData> {
    logger.info(`Starting scrape-only process for: ${scheduleUrl}`);
    return await this.scraper.scrapeCompetition(scheduleUrl);
  }

  getProgress(sessionId?: string): ScrapingProgress | undefined {
    return this.scraper.getProgress(sessionId);
  }

  getAllSessions(): ScrapingProgress[] {
    return this.scraper.getAllSessions();
  }

  getProgressManager(): ProgressManager {
    return this.progressManager;
  }
}

export { CompetitionScraper, DataExporter };
export * from '../models/types';
