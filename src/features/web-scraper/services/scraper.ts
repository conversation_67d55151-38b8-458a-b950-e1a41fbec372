import { logger } from '../../../shared/lib/logger';
import { WebFetcher } from './web-fetcher';
import { HtmlParser } from './html-parser';
import { ProgressManager } from './progress-manager';
import { CompetitionData, ScheduleEvent, HeatResult, ScrapingProgress, DataQualityMetrics } from '../models/types';

export class CompetitionScraper {
  private webFetcher: WebFetcher;
  private htmlParser: HtmlParser;
  private progressManager: ProgressManager;
  private currentSessionId?: string;

  constructor(progressManager?: ProgressManager) {
    this.webFetcher = new WebFetcher({
      timeout: 15000,
      retryAttempts: 3,
      enableCache: true,
      cacheTtl: 300000, // 5 minutes
      enableRateLimit: true,
      requestsPerSecond: 2, // Be respectful to the server
    });
    this.htmlParser = new HtmlParser();
    this.progressManager = progressManager || new ProgressManager();
  }

  async scrapeCompetition(scheduleUrl: string, sessionId?: string): Promise<CompetitionData> {
    logger.info(`Starting competition scrape from: ${scheduleUrl}`);
    const startTime = Date.now();

    // Create or use provided session ID
    this.currentSessionId = sessionId || this.generateSessionId();
    const progress = this.progressManager.createSession(this.currentSessionId);

    try {
      const baseUrl = this.extractBaseUrl(scheduleUrl);

      // Step 1: Fetch and parse schedule
      const scheduleHtml = await this.webFetcher.fetchHtml(scheduleUrl);
      const events = this.htmlParser.parseSchedule(scheduleHtml, baseUrl);

      // Start the session with total events
      this.progressManager.startSession(this.currentSessionId, events.length);
      logger.info(`Found ${events.length} events to process`);

      // Step 2: Process each event and its heats
      const heats: HeatResult[] = [];
      const dataQuality: DataQualityMetrics = {
        totalAthletes: 0,
        athletesWithResults: 0,
        athletesWithoutResults: 0,
        eventsWithAllHeats: 0,
        eventsWithMissingHeats: 0,
        averageAthletesPerHeat: 0,
      };

      for (const event of events) {
        const eventStartTime = Date.now();
        try {
          logger.info(`Processing event: ${event.eventName}`);
          const eventHeats = await this.scrapeEventHeats(event, baseUrl);
          heats.push(...eventHeats);

          // Update data quality metrics
          const eventAthletes = eventHeats.reduce((sum, heat) => sum + heat.athletes.length, 0);
          dataQuality.totalAthletes += eventAthletes;

          const athletesWithResults = eventHeats.reduce((sum, heat) =>
            sum + heat.athletes.filter(a => a.result && a.result !== '-').length, 0);
          dataQuality.athletesWithResults += athletesWithResults;
          dataQuality.athletesWithoutResults += eventAthletes - athletesWithResults;

          // Check if event has expected number of heats
          if (event.heats > 0 && eventHeats.length === event.heats) {
            dataQuality.eventsWithAllHeats++;
          } else if (event.heats > 0) {
            dataQuality.eventsWithMissingHeats++;
            this.progressManager.addWarning(this.currentSessionId,
              `Event ${event.eventName} expected ${event.heats} heats but found ${eventHeats.length}`);
          }

          this.progressManager.updateEventProgress(this.currentSessionId, event.eventName, eventStartTime);
          this.progressManager.updateDataQuality(this.currentSessionId, dataQuality);
        } catch (error) {
          const scrapingError = this.progressManager.createError(
            'unknown',
            `Failed to process event ${event.eventName}: ${error}`,
            event.eventLink,
            true
          );
          this.progressManager.addError(this.currentSessionId, scrapingError);
        }
      }

      const competitionData: CompetitionData = {
        baseUrl,
        scheduleUrl,
        events,
        heats,
        scrapedAt: new Date(),
      };

      const duration = Date.now() - startTime;
      const currentProgress = this.progressManager.getProgress(this.currentSessionId);

      logger.info(
        `Competition scraping completed in ${duration}ms. Events: ${events.length}, Heats: ${heats.length}, Errors: ${currentProgress?.errors.length || 0}`
      );

      // Complete the session successfully
      this.progressManager.completeSession(this.currentSessionId, true);

      return competitionData;
    } catch (error) {
      logger.error('Competition scraping failed:', error);

      // Complete the session with error
      if (this.currentSessionId) {
        const scrapingError = this.progressManager.createError(
          'unknown',
          `Competition scraping failed: ${error}`,
          scheduleUrl,
          false
        );
        this.progressManager.addError(this.currentSessionId, scrapingError);
        this.progressManager.completeSession(this.currentSessionId, false);
      }

      throw error;
    }
  }

  private async scrapeEventHeats(event: ScheduleEvent, baseUrl: string): Promise<HeatResult[]> {
    const heats: HeatResult[] = [];
    let currentHeatUrl: string | undefined = event.eventLink;
    let heatCount = 0;
    const maxHeats = event.heats || 10; // Fallback to prevent infinite loops

    while (currentHeatUrl && heatCount < maxHeats) {
      try {
        logger.debug(`Fetching heat ${heatCount + 1} for ${event.eventName}: ${currentHeatUrl}`);

        const heatHtml = await this.webFetcher.fetchHtml(currentHeatUrl);
        const heatResult = this.htmlParser.parseHeatResults(
          heatHtml,
          event.eventName,
          currentHeatUrl
        );

        heats.push(heatResult);
        heatCount++;

        // Update progress tracking
        if (this.currentSessionId) {
          this.progressManager.updateHeatProgress(this.currentSessionId, heatResult.heatNumber);
        }

        // Move to next heat if available
        currentHeatUrl = heatResult.nextHeatLink;

        if (currentHeatUrl) {
          // Add small delay between requests to be respectful
          await this.delay(500);
        }
      } catch (error) {
        if (this.currentSessionId) {
          const scrapingError = this.progressManager.createError(
            'network',
            `Failed to fetch heat ${heatCount + 1} for ${event.eventName}: ${error}`,
            currentHeatUrl,
            true
          );
          this.progressManager.addError(this.currentSessionId, scrapingError);
        }
        break;
      }
    }

    logger.info(`Scraped ${heats.length} heats for ${event.eventName}`);
    return heats;
  }

  private extractBaseUrl(url: string): string {
    const urlObj = new URL(url);
    const pathParts = urlObj.pathname.split('/');
    pathParts.pop(); // Remove filename
    return `${urlObj.origin}${pathParts.join('/')}/`;
  }

  getProgress(sessionId?: string): ScrapingProgress | undefined {
    const id = sessionId || this.currentSessionId;
    return id ? this.progressManager.getProgress(id) : undefined;
  }

  getAllSessions(): ScrapingProgress[] {
    return this.progressManager.getAllSessions();
  }

  getProgressManager(): ProgressManager {
    return this.progressManager;
  }

  private generateSessionId(): string {
    return `scrape_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}
