import { EventEmitter } from 'events';
import { logger } from '../../../shared/lib/logger';
import { ScrapingProgress, ScrapingError, DataQualityMetrics } from '../models/types';

export class ProgressManager extends EventEmitter {
  private sessions: Map<string, ScrapingProgress> = new Map();
  private eventTimes: number[] = [];

  createSession(sessionId: string): ScrapingProgress {
    const progress: ScrapingProgress = {
      sessionId,
      status: 'idle',
      totalEvents: 0,
      processedEvents: 0,
      totalHeats: 0,
      processedHeats: 0,
      errors: [],
      warnings: [],
      dataQuality: {
        totalAthletes: 0,
        athletesWithResults: 0,
        athletesWithoutResults: 0,
        eventsWithAllHeats: 0,
        eventsWithMissingHeats: 0,
        averageAthletesPerHeat: 0,
      },
    };

    this.sessions.set(sessionId, progress);
    logger.info(`Created scraping session: ${sessionId}`);
    return progress;
  }

  startSession(sessionId: string, totalEvents: number): void {
    const progress = this.sessions.get(sessionId);
    if (!progress) {
      throw new Error(`Session ${sessionId} not found`);
    }

    progress.status = 'running';
    progress.startTime = new Date();
    progress.totalEvents = totalEvents;
    this.eventTimes = []; // Reset timing data

    this.updateSession(sessionId, progress);
    logger.info(`Started scraping session: ${sessionId} with ${totalEvents} events`);
  }

  updateEventProgress(sessionId: string, eventName: string, eventStartTime: number): void {
    const progress = this.sessions.get(sessionId);
    if (!progress) return;

    progress.currentEvent = eventName;
    progress.processedEvents++;

    // Calculate timing metrics
    const eventDuration = Date.now() - eventStartTime;
    this.eventTimes.push(eventDuration);
    
    if (this.eventTimes.length > 0) {
      progress.averageEventTime = this.eventTimes.reduce((a, b) => a + b, 0) / this.eventTimes.length;
      
      const remainingEvents = progress.totalEvents - progress.processedEvents;
      progress.estimatedTimeRemaining = remainingEvents * progress.averageEventTime;
    }

    this.updateSession(sessionId, progress);
  }

  updateHeatProgress(sessionId: string, heatNumber: number): void {
    const progress = this.sessions.get(sessionId);
    if (!progress) return;

    progress.currentHeat = heatNumber;
    progress.processedHeats++;

    this.updateSession(sessionId, progress);
  }

  addError(sessionId: string, error: ScrapingError): void {
    const progress = this.sessions.get(sessionId);
    if (!progress) return;

    progress.errors.push(error);
    logger.error(`Scraping error in session ${sessionId}:`, error);

    this.updateSession(sessionId, progress);
  }

  addWarning(sessionId: string, warning: string): void {
    const progress = this.sessions.get(sessionId);
    if (!progress) return;

    progress.warnings.push(warning);
    logger.warn(`Scraping warning in session ${sessionId}: ${warning}`);

    this.updateSession(sessionId, progress);
  }

  updateDataQuality(sessionId: string, metrics: Partial<DataQualityMetrics>): void {
    const progress = this.sessions.get(sessionId);
    if (!progress) return;

    Object.assign(progress.dataQuality, metrics);
    this.updateSession(sessionId, progress);
  }

  completeSession(sessionId: string, success: boolean = true): void {
    const progress = this.sessions.get(sessionId);
    if (!progress) return;

    progress.status = success ? 'completed' : 'error';
    progress.endTime = new Date();

    // Calculate final data quality metrics
    if (progress.processedHeats > 0) {
      progress.dataQuality.averageAthletesPerHeat = 
        progress.dataQuality.totalAthletes / progress.processedHeats;
    }

    this.updateSession(sessionId, progress);
    logger.info(`Completed scraping session: ${sessionId} (${success ? 'success' : 'error'})`);

    // Clean up old sessions after some time
    setTimeout(() => {
      this.sessions.delete(sessionId);
      logger.debug(`Cleaned up session: ${sessionId}`);
    }, 300000); // 5 minutes
  }

  getProgress(sessionId: string): ScrapingProgress | undefined {
    return this.sessions.get(sessionId);
  }

  getAllSessions(): ScrapingProgress[] {
    return Array.from(this.sessions.values());
  }

  private updateSession(sessionId: string, progress: ScrapingProgress): void {
    this.sessions.set(sessionId, progress);
    
    // Emit progress update event for real-time tracking
    this.emit('progress', {
      sessionId,
      progress: { ...progress }, // Clone to prevent mutations
    });
  }

  // Helper method to create error objects
  createError(
    type: ScrapingError['type'],
    message: string,
    context?: string,
    retryable: boolean = false
  ): ScrapingError {
    return {
      timestamp: new Date(),
      type,
      message,
      context,
      retryable,
    };
  }
}
