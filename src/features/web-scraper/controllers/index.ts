import { Request, Response, NextFunction } from 'express';
import { WebScraperService } from '../services';
import { logger } from '../../../shared/lib/logger';

export const scrapeCompetition = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    // Check if req.body exists and is an object
    if (!req.body || typeof req.body !== 'object') {
      logger.error('Request body is missing or invalid:', {
        body: req.body,
        bodyType: typeof req.body,
        contentType: req.get('Content-Type'),
        headers: req.headers
      });
      res.status(400).json({
        message: 'Request body is required and must be valid JSON',
        details: 'Make sure to send Content-Type: application/json header and valid JSON body'
      });
      return;
    }

    const { scheduleUrl, exportEndpoint, apiKey } = req.body;

    if (!scheduleUrl) {
      res.status(400).json({
        message: 'scheduleUrl is required',
        receivedBody: req.body
      });
      return;
    }

    logger.info(`Received scrape request for: ${scheduleUrl}`);

    const exportConfig = exportEndpoint ? {
      endpointUrl: exportEndpoint,
      apiKey,
    } : undefined;

    const scraperService = new WebScraperService(exportConfig);
    const competitionData = await scraperService.scrapeAndExport(scheduleUrl);

    res.json({
      success: true,
      data: competitionData,
      summary: {
        totalEvents: competitionData.events.length,
        totalHeats: competitionData.heats.length,
        totalAthletes: competitionData.heats.reduce((sum, heat) => sum + heat.athletes.length, 0),
      },
    });
  } catch (error) {
    logger.error('Scrape competition controller error:', error);
    next(error);
  }
};

export const getScrapingProgress = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const { sessionId } = req.query;
    const scraperService = new WebScraperService();

    if (sessionId && typeof sessionId === 'string') {
      // Get specific session progress
      const progress = scraperService.getProgress(sessionId);
      if (progress) {
        res.json({ progress });
      } else {
        res.status(404).json({ message: `Session ${sessionId} not found` });
      }
    } else {
      // Get all active sessions
      const allSessions = scraperService.getAllSessions();
      res.json({
        sessions: allSessions,
        count: allSessions.length
      });
    }
  } catch (error) {
    logger.error('Get scraping progress error:', error);
    next(error);
  }
};
