import { Request, Response, NextFunction } from 'express';
import { competitionEntryService } from '../services';
import { logger } from '../../../shared/lib/logger';

export const getEntriesByCompetition = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const competitionId = parseInt(req.params.competitionId, 10);
    
    if (isNaN(competitionId)) {
      res.status(400).json({ message: 'Invalid competition ID' });
      return;
    }
    
    const entries = await competitionEntryService.getByCompetitionId(competitionId);
    res.json({ data: entries });
  } catch (error) {
    next(error);
  }
};

export const getEntryById = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const id = parseInt(req.params.id, 10);
    
    if (isNaN(id)) {
      res.status(400).json({ message: 'Invalid entry ID' });
      return;
    }
    
    const entry = await competitionEntryService.getById(id);
    
    if (!entry) {
      res.status(404).json({ message: 'Entry not found' });
      return;
    }
    
    res.json({ data: entry });
  } catch (error) {
    next(error);
  }
};

export const getEntriesByAthlete = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const athleteId = parseInt(req.params.athleteId, 10);
    
    if (isNaN(athleteId)) {
      res.status(400).json({ message: 'Invalid athlete ID' });
      return;
    }
    
    const entries = await competitionEntryService.getByAthleteId(athleteId);
    res.json({ data: entries });
  } catch (error) {
    next(error);
  }
};
