import { RowDataPacket } from 'mysql2/promise';

/*
{
				"entryId": 1453,
				"compId": 617,
				"ceId": 70820,
				"egId": 13780,
				"eventName": "4 x 100 JG",
				"athleteId": null,
				"teamBibNo": "77",
				"teamName": "Surrey",
				"present": 1,
				"heatno": 1,
				"laneno": 6,
				"heatnocheckedin": 0,
				"lanenocheckedin": 0
			}
 */
export interface CompetitionEntry extends RowDataPacket {
  entryId: number;
  compId: number;
  ceId: number;
  egId: number;
  eventName: string;
  athleteId: number;
  teamBibNo: string;
  teamName: string;
  present: number;
  heatno: number;
  laneno: number;
  heatnocheckedin: number;
  lanenocheckedin: number;
}
