import { dbConnect } from '../../../shared/lib/db';
import { CompetitionEntry } from '../models/types';

const competitionEntryService = {
  getByCompetitionId: async (competitionId: number): Promise<CompetitionEntry[]> => {
    const sql = `Select * from Entry4_uk_IndivEntriesView where compid = ?`;

    return await dbConnect.query<CompetitionEntry[]>(sql, [competitionId]);
  },

  getById: async (id: number): Promise<CompetitionEntry | undefined> => {
    const sql = `
      SELECT 
        e.id,
        e.competition_id as competitionId,
        e.athlete_id as athleteId,
        a.name as athleteName,
        e.category_id as categoryId,
        c.name as categoryName,
        e.entry_date as entryDate,
        e.status,
        e.payment_status as paymentStatus,
        e.notes
      FROM 
        competition_entries e
      JOIN 
        athletes a ON e.athlete_id = a.id
      JOIN 
        categories c ON e.category_id = c.id
      WHERE 
        e.id = ?
    `;

    const entries = await dbConnect.query<CompetitionEntry[]>(sql, [id]);
    return entries[0];
  },

  getByAthleteId: async (athleteId: number): Promise<CompetitionEntry[]> => {
    const sql = `
      SELECT 
        e.id,
        e.competition_id as competitionId,
        e.athlete_id as athleteId,
        a.name as athleteName,
        e.category_id as categoryId,
        c.name as categoryName,
        e.entry_date as entryDate,
        e.status,
        e.payment_status as paymentStatus,
        e.notes
      FROM 
        competition_entries e
      JOIN 
        athletes a ON e.athlete_id = a.id
      JOIN 
        categories c ON e.category_id = c.id
      WHERE 
        e.athlete_id = ?
      ORDER BY 
        e.entry_date DESC
    `;

    return await dbConnect.query<CompetitionEntry[]>(sql, [athleteId]);
  },
};

export { competitionEntryService };
