import express from 'express';
import { getEntriesByCompetition, getEntryById, getEntriesByAthlete } from '../controllers';

const entryRouter = express.Router();

// Add debugging middleware
entryRouter.use((req, res, next) => {
  console.log(`ENTRIES ROUTER: ${req.method} ${req.originalUrl} (${req.url})`);
  next();
});

// Routes for entries (read-only)
entryRouter.get(
  '/competition/:competitionId',
  (req, res, next) => {
    console.log(`Competition entries route hit with ID: ${req.params.competitionId}`);
    next();
  },
  getEntriesByCompetition
);

entryRouter.get('/athlete/:athleteId', getEntriesByAthlete);
entryRouter.get('/:id', getEntryById);

// Add a test route to verify the router is working
entryRouter.get('/test', (req, res) => {
  res.json({ message: 'Entries router test route is working!' });
});

// Export the router
export { entryRouter };
