const express = require('express');
const helmet = require('helmet');
const cors = require('cors');
const rateLimit = require('express-rate-limit');
const { logger } = require('./shared/lib/logger');
const { errorHandler } = require('./shared/middleware/error-handler');
const { apiRouter } = require('./api');
const { dbConnect } = require('./shared/lib/db');

const app = express();

// Security middleware
app.use(helmet());
app.use(cors());
app.use(express.json());

// Rate limiting
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // limit each IP to 100 requests per windowMs
});
app.use(limiter);

// Routes
app.use('/api', apiRouter);

// Error handling
app.use(errorHandler);

// Start server
const APP_PORT = process.env.APP_PORT || 3000;

const startServer = async () => {
  try {
    // Test database connection
    await dbConnect.testConnection();
    logger.info('Database connection successful');

    app.listen(APP_PORT, () => {
      logger.info(`App.js Server running on port ${APP_PORT}`);
    });
  } catch (error) {
    logger.error('Failed to start server:', error);
    process.exit(1);
  }
};

startServer();

module.exports = { app };
