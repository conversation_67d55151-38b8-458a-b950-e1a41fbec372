import express from 'express';

const app = express();

// Root level logging
app.use((req, res, next) => {
  console.log(`Debug server: ${req.method} ${req.url}`);
  next();
});

// Create a router for API
const apiRouter = express.Router();

// Log all requests to the API router
apiRouter.use((req, res, next) => {
  console.log(`Debug Server API router: ${req.method} ${req.url}`);
  next();
});

// Create a router for entries
const entriesRouter = express.Router();

// Log all requests to the entries router
entriesRouter.use((req, res, next) => {
  console.log(`Entries router: ${req.method} ${req.url}`);
  next();
});

// Add the specific route we're having trouble with
entriesRouter.get('/competition/:competitionId', (req, res) => {
  console.log(`Competition ID: ${req.params.competitionId}`);
  res.json({
    message: 'Entries by competition route is working!',
    competitionId: req.params.competitionId,
  });
});

// Mount the entries router at /entries
apiRouter.use('/entries', entriesRouter);

// Mount the API router at /api
app.use('/api', apiRouter);

// Start the server
const PORT = 3004;
app.listen(PORT, () => {
  console.log(`Debug server running on port ${PORT}`);
});
