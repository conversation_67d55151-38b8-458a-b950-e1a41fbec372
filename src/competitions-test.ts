import express from 'express';
import { competitionRouter } from './features/competitions/api';

const app = express();

// Root level logging
app.use((req, res, next) => {
  console.log(`Competitions test server: ${req.method} ${req.url}`);
  next();
});

// Mount the competitions router directly
console.log('Mounting competitions router at /competitions');
app.use('/competitions', competitionRouter);

// Add a root route
app.get('/', (req, res) => {
  console.log('Root route hit');
  res.send('Competitions test server is working!');
});

// Start the server
const PORT = 3005;
app.listen(PORT, () => {
  console.log(`Competitions test server running on port ${PORT}`);
  console.log(`Test the server at: http://localhost:${PORT}/`);
  console.log(`Test the competitions route at: http://localhost:${PORT}/competitions/123`);
  console.log(`Test the competitions test route at: http://localhost:${PORT}/competitions/test`);
});