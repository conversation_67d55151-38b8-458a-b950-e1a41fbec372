<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Server Admin Dashboard</title>
  <style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    .card { border: 1px solid #ddd; border-radius: 4px; padding: 15px; margin-bottom: 15px; }
    .competition { margin-bottom: 10px; padding: 10px; background-color: #f5f5f5; }
    .room { margin-bottom: 5px; }
    h2 { margin-top: 0; }
  </style>
</head>
<body>
  <h1>Server Admin Dashboard</h1>

  <div class="card">
    <h2>Cache Status</h2>
    <div id="cache-info"></div>
  </div>

  <div class="card">
    <h2>Socket Connections</h2>
    <div id="socket-info"></div>
  </div>

  <div class="card">
    <h2>Cache Management</h2>
    <div>
      <input type="number" id="refreshCompId" placeholder="Competition ID">
      <button id="refreshCacheBtn">Refresh Cache</button>
      <div id="refreshResult"></div>
    </div>
  </div>

  <script>
    function fetchServerStatus() {
      fetch('/api/admin/status')
        .then(response => response.json())
        .then(data => {
          displayCacheInfo(data.data.cache);
          displaySocketInfo(data.data.sockets);
        })
        .catch(error => console.error('Error fetching server status:', error));
    }

    function displayCacheInfo(cache) {
      const container = document.getElementById('cache-info');
      container.innerHTML = `<p>Total competitions in cache: ${cache.competitionCount}</p>`;

      cache.competitions.forEach(comp => {
        const compDiv = document.createElement('div');
        compDiv.className = 'competition';
        compDiv.innerHTML = `
          <strong>Competition ID: ${comp.id}</strong><br>
          Cached since: ${new Date(comp.cachedSince).toLocaleString()}<br>
          Message count: ${comp.messageCount}
        `;
        container.appendChild(compDiv);
      });
    }

    function displaySocketInfo(sockets) {
      const container = document.getElementById('socket-info');
      container.innerHTML = `<p>Total connections: ${sockets.totalConnections}</p>`;

      Object.entries(sockets.rooms).forEach(([roomName, clientCount]) => {
        const roomDiv = document.createElement('div');
        roomDiv.className = 'room';
        roomDiv.innerHTML = `${roomName}: ${clientCount} clients`;
        container.appendChild(roomDiv);
      });
    }

    // Initial fetch
    fetchServerStatus();

    // Refresh every 10 seconds
    setInterval(fetchServerStatus, 10000);

    document.getElementById('refreshCacheBtn').addEventListener('click', function() {
      const compId = document.getElementById('refreshCompId').value;
      if (!compId) {
        document.getElementById('refreshResult').innerHTML = 'Please enter a competition ID';
        return;
      }

      fetch(`/api/admin/competitions/${compId}/refresh-cache`, {
        method: 'POST'
      })
      .then(response => response.json())
      .then(data => {
        document.getElementById('refreshResult').innerHTML =
          `<p>${data.message}</p>
           <p>Cached at: ${new Date(data.data.cachedAt).toLocaleString()}</p>
           <p>Message count: ${data.data.messageCount}</p>`;

        // Refresh the status display
        fetchServerStatus();
      })
      .catch(error => {
        document.getElementById('refreshResult').innerHTML = `Error: ${error.message}`;
      });
    });
  </script>
</body>
</html>
