document.addEventListener('DOMContentLoaded', () => {
  const socket = io(window.location.origin);
  const messagesDiv = document.getElementById('messages');
  const competitionIdInput = document.getElementById('competitionId');
  const joinButton = document.getElementById('joinButton');
  const leaveButton = document.getElementById('leaveButton');
  const pingButton = document.getElementById('pingButton');

  // Generate a unique device key
  const deviceKey = Math.random();

  socket.on('connect', () => {
    addMessage('Connected to server');
  });

  // Listen for competition-specific updates
  socket.on('competition-update', data => {
    addMessage(`Competition Update: ${JSON.stringify(data)}`);
  });

  socket.on('result-update', data => {
    addMessage(`Result Update: ${JSON.stringify(data)}`);
  });

  // Listen for pong responses
  socket.on('pong', data => {
    addMessage(`Pong received: ${JSON.stringify(data)}`);
  });

  // Listen for subscription confirmations
  socket.on('subscribed', data => {
    addMessage(`Subscribed to competition: ${data.competitionId}`);
  });

  socket.on('unsubscribed', data => {
    addMessage(`Unsubscribed from competition: ${data.competitionId}`);
  });

  // General message handler (for backward compatibility)
  socket.on('message', data => {
    addMessage(`General Message: ${JSON.stringify(data)}`);
  });

  // Join a competition room
  joinButton.addEventListener('click', () => {
    const competitionId = parseInt(competitionIdInput.value);
    if (competitionId) {
      // Using the new message format
      const message = {
        key: 0,
        comp: { id: competitionId },
        action: 'subscribe',
        deviceKey: deviceKey,
        securityKey: '',
        domain: window.location.hostname,
        payload: { compId: competitionId },
      };

      socket.emit('message', JSON.stringify(message));
      addMessage(`Sent subscription request for competition: ${competitionId}`);
    } else {
      addMessage('Please enter a valid competition ID');
    }
  });

  // Leave a competition room
  leaveButton.addEventListener('click', () => {
    const competitionId = parseInt(competitionIdInput.value);
    if (competitionId) {
      // Using the new message format
      const message = {
        key: 0,
        comp: { id: competitionId },
        action: 'unsubscribe',
        deviceKey: deviceKey,
        securityKey: '',
        domain: window.location.hostname,
        payload: { competitionId: competitionId },
      };

      socket.emit('message', JSON.stringify(message));
      addMessage(`Sent unsubscribe request for competition: ${competitionId}`);
    } else {
      addMessage('Please enter a valid competition ID');
    }
  });

  // Send ping
  pingButton.addEventListener('click', () => {
    const competitionId = parseInt(competitionIdInput.value) || 0;
    const message = {
      key: 0,
      comp: { id: competitionId },
      action: 'ping',
      deviceKey: deviceKey,
      securityKey: '',
      domain: window.location.hostname,
      payload: { ping: true },
    };

    socket.emit('message', JSON.stringify(message));
    addMessage('Ping sent');
  });

  function addMessage(text) {
    const messageElement = document.createElement('div');
    messageElement.textContent = text;
    messagesDiv.appendChild(messageElement);
  }
});
