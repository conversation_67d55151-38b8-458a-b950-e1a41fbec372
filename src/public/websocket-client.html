<!DOCTYPE html>
<html>
<head>
  <title>WebSocket Client</title>
  <script src="/socket.io/socket.io.js"></script>
  <script src="/websocket-client.js"></script>
</head>
<body>
  <h1>WebSocket Competition Client</h1>
  <div>
    <label for="competitionId">Competition ID:</label>
    <input type="number" id="competitionId" placeholder="Enter competition ID">
    <button id="joinButton">Subscribe</button>
    <button id="leaveButton">Unsubscribe</button>
    <button id="pingButton">Ping</button>
  </div>
  <h2>Messages:</h2>
  <div id="messages"></div>
</body>
</html>
