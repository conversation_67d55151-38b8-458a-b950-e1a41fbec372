import express from 'express';
import helmet from 'helmet';
import cors from 'cors';
import { apiRouter } from './api';
import { dbRateLimiter } from './shared/middleware/db-rate-limiter';
import path from 'path';

// Create Express application
const app = express();

// Add debugging middleware
app.use((req, res, next) => {
  console.log(`APP: ${req.method} ${req.originalUrl} (${req.url})`);
  next();
});

// Basic middleware
app.use(express.json());
app.use(
  cors({
    origin: '*', // Allow all origins in development
    // For production, you might want to restrict this:
    // origin: ["https://your-domain.com", "http://localhost:3009"],
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
    credentials: true,
  })
);
app.use(
  helmet({
    contentSecurityPolicy: {
      directives: {
        defaultSrc: ["'self'"],
        scriptSrc: ["'self'", "'unsafe-inline'", 'cdn.socket.io'],
        connectSrc: ["'self'", 'ws:', 'wss:'],
        upgradeInsecureRequests: [],
      },
    },
  })
);

// Handle favicon requests
app.get('/favicon.ico', (req, res) => {
  res.status(204).end(); // No content response
});

// Add a test route to verify the app is working
app.get('/test', (req, res) => {
  console.log('App test route hit');
  res.send('App test route is working!');
});

// Add a debug route
app.get('/debug', (req, res) => {
  res.send('Server is running!');
});

// Add before API routes
const rateLimitEnabled = process.env.RATE_LIMIT_ENABLED !== 'false';
app.use('/api', dbRateLimiter(100, 60000, rateLimitEnabled));
app.use('/api', apiRouter);

// Serve static files from the public directory
app.use(express.static(path.join(__dirname, 'public')));

// Serve static files from the client directory
app.use('/liteclient', express.static(path.join(__dirname, 'liteclient')));

// Add a specific route for the client index.html
app.get('/liteclient', (req, res) => {
  res.sendFile(path.join(__dirname, 'liteclient', 'index.html'));
});

// Add before the catch-all route
app.get('/ws-client', (req, res) => {
  const filePath = path.join(__dirname, 'public', 'websocket-client.html');
  console.log('Attempting to serve file from:', filePath);

  // Check if file exists
  const fs = require('fs');
  if (fs.existsSync(filePath)) {
    console.log('File exists, sending...');
    res.sendFile(filePath);
  } else {
    console.log('File does not exist!');
    res.status(404).send('WebSocket client file not found');
  }
});

// Add this route before the catch-all route
app.get('/admin-dashboard', (req, res) => {
  res.sendFile(path.join(__dirname, 'public', 'admin-dashboard.html'));
});

// IMPORTANT: Fix the catch-all route

// To this:
app.use('/', (req, res) => {
  console.log(`No route matched: ${req.method} ${req.originalUrl}`);
  res.status(404).send('Route not found');
});

// Export the app for use in index.ts
export { app };
