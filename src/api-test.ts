import express from 'express';

// Create a new router instance
const testApiRouter = express.Router();

// Add debugging middleware
testApiRouter.use((req, res, next) => {
  console.log(`TEST API ROUTER: ${req.method} ${req.originalUrl} (${req.url})`);
  next();
});

// Add a test route
testApiRouter.get('/test', (req, res) => {
  console.log('Test API route hit');
  res.json({ message: 'Test API route is working!' });
});

// Add a competitions route
testApiRouter.get('/competitions/:id', (req, res) => {
  console.log(`Competition route hit with ID: ${req.params.id}`);
  res.json({ message: 'Competition route is working!', id: req.params.id });
});

// Add a catch-all route
testApiRouter.use('/', (req, res) => {
  console.log(`No test API route matched: ${req.method} ${req.originalUrl}`);
  res.status(404).json({ message: 'Test API route not found' });
});

export { testApiRouter };