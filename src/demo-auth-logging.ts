/**
 * Demo script to show authentication and authorization logging in action
 * This script simulates various scenarios where users are denied access
 */

import express from 'express';
import { hasRole, hasPermission, hasAnyPermission, hasAllPermissions } from './features/auth/middleware/rbac';
import { verifyToken, requireAuth } from './features/auth/middleware/auth';
import { PERMISSIONS } from './features/auth/config/roles';
import { logger } from './shared/lib/logger';

const app = express();
app.use(express.json());

// Middleware to simulate different user scenarios
const simulateUser = (userType: 'none' | 'no-roles' | 'user' | 'admin' | 'invalid-token') => {
  return (req: express.Request, res: express.Response, next: express.NextFunction) => {
    switch (userType) {
      case 'none':
        // No user - simulates unauthenticated request
        req.user = undefined;
        break;
      case 'no-roles':
        // User with no roles assigned
        req.user = {
          id: 'user-no-roles',
          email: '<EMAIL>',
          roles: null,
        };
        break;
      case 'user':
        // Regular user
        req.user = {
          id: 'user-123',
          email: '<EMAIL>',
          roles: ['user'],
        };
        break;
      case 'admin':
        // Admin user
        req.user = {
          id: 'admin-123',
          email: '<EMAIL>',
          roles: ['admin'],
        };
        break;
      case 'invalid-token':
        // Simulate invalid token by not setting user
        req.user = undefined;
        break;
    }
    next();
  };
};

// Demo routes that will trigger different logging scenarios

// 1. Route that requires admin role - will log when non-admin tries to access
app.get('/demo/admin-only/:userType', 
  simulateUser('none'), // This will be overridden by the userType param
  (req, res, next) => {
    // Override user based on URL parameter
    const userType = req.params.userType as any;
    simulateUser(userType)(req, res, next);
  },
  hasRole('admin'),
  (req, res) => {
    res.json({ message: 'Admin access granted', user: req.user });
  }
);

// 2. Route that requires specific permission
app.get('/demo/manage-users/:userType',
  (req, res, next) => {
    const userType = req.params.userType as any;
    simulateUser(userType)(req, res, next);
  },
  hasPermission(PERMISSIONS.MANAGE_USERS),
  (req, res) => {
    res.json({ message: 'User management access granted', user: req.user });
  }
);

// 3. Route that requires any of multiple permissions
app.get('/demo/view-content/:userType',
  (req, res, next) => {
    const userType = req.params.userType as any;
    simulateUser(userType)(req, res, next);
  },
  hasAnyPermission([PERMISSIONS.VIEW_COMPETITIONS, PERMISSIONS.VIEW_ENTRIES]),
  (req, res) => {
    res.json({ message: 'Content viewing access granted', user: req.user });
  }
);

// 4. Route that requires all of multiple permissions
app.get('/demo/full-access/:userType',
  (req, res, next) => {
    const userType = req.params.userType as any;
    simulateUser(userType)(req, res, next);
  },
  hasAllPermissions([PERMISSIONS.VIEW_COMPETITIONS, PERMISSIONS.EDIT_COMPETITION, PERMISSIONS.DELETE_COMPETITION]),
  (req, res) => {
    res.json({ message: 'Full access granted', user: req.user });
  }
);

// 5. Route that requires authentication
app.get('/demo/auth-required/:userType',
  (req, res, next) => {
    const userType = req.params.userType as any;
    simulateUser(userType)(req, res, next);
  },
  requireAuth,
  (req, res) => {
    res.json({ message: 'Authentication successful', user: req.user });
  }
);

// Demo endpoint to show all available test scenarios
app.get('/demo', (req, res) => {
  res.json({
    message: 'Authentication & Authorization Logging Demo',
    scenarios: {
      'No authentication': '/demo/admin-only/none',
      'No roles assigned': '/demo/admin-only/no-roles', 
      'User trying admin route': '/demo/admin-only/user',
      'Admin accessing admin route': '/demo/admin-only/admin',
      'User trying to manage users': '/demo/manage-users/user',
      'User trying to view content': '/demo/view-content/user',
      'User trying full access': '/demo/full-access/user',
      'Unauthenticated auth required': '/demo/auth-required/none',
    },
    instructions: 'Visit any of the scenario URLs above to see logging in action. Check the logs directory for detailed access denial logs.'
  });
});

const PORT = process.env.DEMO_PORT || 3001;

if (require.main === module) {
  app.listen(PORT, () => {
    logger.info(`Auth logging demo server running on port ${PORT}`);
    logger.info('Visit http://localhost:' + PORT + '/demo to see available test scenarios');
    console.log(`Auth logging demo server running on port ${PORT}`);
    console.log('Visit http://localhost:' + PORT + '/demo to see available test scenarios');
  });
}

export { app };
